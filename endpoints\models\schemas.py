import uuid
from datetime import date, datetime
from typing import Optional, ClassVar, Union, Sequence, TypeVar, Generic, Dict, Any, List
from uuid import UUID
from pydantic import BaseModel, EmailStr, constr, UUID4, condecimal, ConfigDict, field_validator, Field, HttpUrl



class UserCreate(BaseModel):
    auth_provider: constr(max_length=50)
    firebase_uid: constr(max_length=28)
    auth_email: EmailStr
    first_name: constr(max_length=255)
    last_name: constr(max_length=255)
    company_name: Optional[constr(max_length=255)] = None
    business_email: EmailStr
    user_agreement_accepted: bool = False
    ip_address: Optional[str] = None
    device_type: Optional[str] = None
    promo_code: Optional[str] = None

    model_config: ClassVar[ConfigDict] = ConfigDict(from_attributes=True)


class UserCreateResponse(UserCreate):
    token: str
    user_id: UUID
    is_user_provisioned: bool
    is_user_locked_in: bool
    plan_id: Optional[UUID] = None

    model_config: ClassVar[ConfigDict] = ConfigDict(from_attributes=True)


class CustomResponse(BaseModel):
    message: str


class SalesSmartStorySubscriptionCreate(BaseModel):
    subscription_id: Optional[UUID] = None
    user_id: UUID
    plan_id: UUID
    plan_name: Optional[str] = None
    transaction_id: Optional[UUID] = None
    transaction_date: Optional[date] = None
    dollar_amt: Optional[condecimal(max_digits=10, decimal_places=2)] = None
    current_period_start: Optional[datetime] = None
    current_period_end: Optional[datetime] = None

    model_config: ClassVar[ConfigDict] = ConfigDict(from_attributes=True)


class GetSubscriptionResponse(SalesSmartStorySubscriptionCreate):
    # plan_id: UUID
    # plan_name: Optional[str] = None
    model_config: ClassVar[ConfigDict] = ConfigDict(from_attributes=True)


class UserProfileResponse(BaseModel):
    user_id: uuid.UUID
    auth_provider: str
    firebase_uid: constr(max_length=28)
    auth_email: EmailStr
    first_name: Optional[str] = None
    last_name: Optional[str] = None
    company_name: Optional[str] = None
    business_email: Optional[EmailStr] = None
    crm_user_sync_pending: bool
    active_status: bool
    created_at: Optional[datetime]
    updated_at: Optional[datetime]

    model_config: ClassVar[ConfigDict] = ConfigDict(from_attributes=True)


# If you want a merged response model that includes both user info + token
class RegistrationResponse(BaseModel):
    message: str
    auth_email: EmailStr
    access_token: str | None = None  # optional if you choose to generate a token
    token_type: str | None = None

    model_config: ClassVar[ConfigDict] = ConfigDict(from_attributes=True)


# user response model
class ProfileResponse(BaseModel):
    user_id: UUID
    firebase_uid: str
    auth_provider: str
    first_name: str | None = None
    last_name: str | None = None
    business_email: EmailStr | None = None
    company_name: str | None = None
    is_user_provisioned: bool
    plan_id: UUID  # to send to figure out if user is paid, free or trial and also to get the active plan of user.
    auth_email: EmailStr

    model_config: ClassVar[ConfigDict] = ConfigDict(from_attributes=True)


class AuthenticationTokenResponse(ProfileResponse):
    # todo rename this bearer_token (from token)
    token: str
    is_user_provisioned: bool
    is_user_locked_in: bool
    plan_id: Optional[UUID] = None
    model_config: ClassVar[ConfigDict] = ConfigDict(from_attributes=True)


class LoginRequest(BaseModel):
    email: EmailStr


class TokenResponse(BaseModel):
    access_token: str
    user_id: UUID
    is_user_provisioned: bool
    is_user_locked_in: bool
    user_plan: Optional[UUID] = None

class UserProfileParams(BaseModel):
    name: str
    frontend_uid: str
    auth_email: EmailStr
    auth_provider: str

    @field_validator("auth_provider", mode="before")
    def validate_auth_provider(cls, value: str) -> str:
        allowed = {"google", "microsoft"}
        value_lower = value.lower()
        if value_lower not in allowed:
            raise ValueError("auth_provider must be either 'google' or 'microsoft'")
        return value_lower

class HomepagePreviousRuns(BaseModel):
    run_id: UUID
    status: str #"success", "failed", "in_progress"
    name : Optional[str]  = "No Name Defined"
    # first_name: str | None = "No First Name Defined"  # Make these fields optional
    # last_name: str | None = "No Last Name Defined"  # Make these fields optional
    company_name: Optional[str] = "No Company Name Defined"  # Make these fields optional
    created_at: datetime

    model_config: ClassVar[ConfigDict] = ConfigDict(from_attributes=True)
#     the previous runs will be in desc order based on the created_at

class ProspectDetail(BaseModel):
    prospect_first_name: str = " "
    prospect_last_name: str | None = " "
    email: Optional[EmailStr] = None
    company: Optional[str] = None
    title: Optional[str] = None
    phone: Optional[str] = None
    prospect_linkedin: Optional[str] = None
    prospect_company_industry: Optional[str] = None
    prospect_company_url: Optional[str] = None
    profile_picture_url: Optional[str] = None
    model_config: ClassVar[ConfigDict] = ConfigDict(from_attributes=True)

class Tabs(BaseModel):
    id: str
    name: str
    content: Optional[str] = "SmartStory generation in progress..."
    is_active: bool


class StoryResponse(BaseModel):
    run_id: UUID
    status: str #todo
    user_id: UUID
    title: str
    overall_summary: Optional[Dict | str] = None
    executive_summary: Optional[Dict | str] = None
    prospect: ProspectDetail
    linkedin_url: Optional[str] = None
    profile_picture_url: Optional[str] = None
    tabs: Optional[List[Tabs]] = None

    created_at: datetime
    updated_at: datetime

    model_config: ClassVar[ConfigDict] = ConfigDict(from_attributes=True)

class PublicStoryResponse(StoryResponse):
    pass

class GovStoryResponse(StoryResponse):
    pass

class UserHomePageResponse(BaseModel):
    user_id: UUID
    auth_email: EmailStr
    first_name: Optional[str] = None
    last_name: Optional[str] = None
    company_name: Optional[str] = None
    business_email: Optional[EmailStr] = None
    linkedin_url: Optional[str] = None
    last_generated_story: Optional[Union[PublicStoryResponse, GovStoryResponse]] = None  # Fixed type hint
    credits_left: int
    previous_story_details: Optional[List[HomepagePreviousRuns]] = None

    model_config: ClassVar[ConfigDict] = ConfigDict(
        from_attributes=True
    )

# ignore this
class StoryGenResponse(BaseModel):
    run_id: UUID4
    credits_left: int

    model_config: ClassVar[ConfigDict] = ConfigDict(from_attributes=True)

AllowedDataTypes = Union[
    UserCreate,
    ProfileResponse,
    RegistrationResponse,
    SalesSmartStorySubscriptionCreate,
    Sequence[UserProfileResponse],
    GetSubscriptionResponse,
    AuthenticationTokenResponse,
    PublicStoryResponse,
    GovStoryResponse,
    UserCreateResponse,
    UserHomePageResponse,
    StoryGenResponse,
    HomepagePreviousRuns,
    List[HomepagePreviousRuns],
    str,
]

# Define a bounded TypeVar that only accepts the allowed types
# T = TypeVar("T")
T = TypeVar("T", bound=AllowedDataTypes)


# Base response class with constrained generic data type
class CustomDataResponse(BaseModel, Generic[T]):
    status_code: int
    detail: str
    data: Optional[T] = None

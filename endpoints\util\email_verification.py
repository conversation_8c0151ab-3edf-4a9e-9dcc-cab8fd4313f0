import logging
import quickemailverification

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


def validate_email(email: str) -> bool:
    """
    Validate an email address using the QuickEmailVerification API.

    :param email: The email address to validate.
    :return: True if the email is considered valid, False otherwise.
    {
    "result": "invalid",
    "reason": "rejected_email",
    "disposable": "false",
    "accept_all": "false",
    "role": "false",
    "free": "true",
    "email": "<EMAIL>",
    "user": "bala",
    "domain": "gmail.com",
    "mx_record": "gmail-smtp-in.l.google.com",
    "mx_domain": "google.com",
    "safe_to_send": "false",
    "did_you_mean": "",
    "success": "true",
    "message": ""
    }
    """
    try:
        # todo remove the api key from here. this is the dev key.
        client = quickemailverification.Client(
            "90d08f2c12e711f751f6e8aeace9a1e5fa73f2d9dad3c3854bc9dc63a90f"
        )
        qe_verification = client.quickemailverification()
        # response = qe_verification.sandbox(email)
        response = qe_verification.verify(email)
        data = response.body
        if data.get("result", "").lower() == "invalid":
            logger.error(f"❌❌❌ Invalid Email address {email}")
            return False
        logger.info(f"✅✅✅ {email} is Valid; returning True")
        return True

    except Exception as e:
        logger.error(f"An error occurred during email validation: {e}")
        return False

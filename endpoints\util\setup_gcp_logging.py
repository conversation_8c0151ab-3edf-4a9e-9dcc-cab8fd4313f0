# my_logging.py

import logging
from google.cloud import logging as gcp_logging

from endpoints.util.environment_utils import EnvironmentUtils


def setup_gcp_logging(logger_name="cloudLogger", level=logging.INFO):
    """
    Configures the root logger to use Google Cloud Logging.

    Args:
        logger_name (str): The name of the logger to configure.
        level (int): The logging level.
    """
    if not EnvironmentUtils.is_running_in_gcp():
        return
    # Create a Google Cloud Logging client
    log_client = gcp_logging.Client()
    # This sets up Cloud Logging as the handler for the root logger
    log_client.setup_logging()

    # Get the logger and set its level
    logger = logging.getLogger(logger_name)
    logger.setLevel(level)

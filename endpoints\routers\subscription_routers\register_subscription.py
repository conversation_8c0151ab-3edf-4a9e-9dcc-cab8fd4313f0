# Subscription API::registerSubscription
# This web service records a subscription in the CloudSQL Subscriptions table using the UserID as a surrogate and grants
# SmartStory credit to the user based on the selected Plan configured in the Plans table. This function also checks if
# the user was a referral, a one-time credit is granted to the user that referred him/her.
#
# Credit is granted one SmartStory per referral for Free, Power, and Growth (User).
#
# The logic also needs to account if the subscription is an upgrade.
# We use the Subscriptions table as a ledger of a user’s history and keep the latest subscription record active with a flag.
# We also have an upgrade only flag.
"""
from fastapi import HTTPException, Depends
from endpoints.models import Subscriptions, Plans, Users  # Assuming models are already defined
from sqlalchemy.orm import Session

from fastapi import APIRouter

register_subscription_router = APIRouter(prefix="/subscription", tags=["Subscription"])


@register_subscription_router.post("/register")
def register_subscription(user_id: int, plan_id: int, referred_by: int = None, db: Session = Depends(get_db)):
    # Fetch plan details
    plan = db.query(Plans).filter(Plans.id == plan_id).first()
    if not plan:
        raise HTTPException(status_code=404, detail="Plan not found")

    # Check for existing active subscription
    active_subscription = db.query(Subscriptions).filter(
        Subscriptions.user_id == user_id,
        Subscriptions.is_active == True
    ).first()

    # If there's an active subscription, mark it inactive for upgrade handling
    if active_subscription:
        if active_subscription.plan_id == plan_id:
            raise HTTPException(status_code=400, detail="User already subscribed to this plan")
        active_subscription.is_active = False
        active_subscription.upgraded = True
        db.add(active_subscription)

    # Create new subscription entry
    new_subscription = Subscriptions(
        user_id=user_id,
        plan_id=plan_id,
        is_active=True
    )
    db.add(new_subscription)

    # Grant SmartStory credit for the selected plan
    user = db.query(Users).filter(Users.id == user_id).first()
    if not user:
        raise HTTPException(status_code=404, detail="User not found")
    user.smartstory_credits += plan.smartstory_credits

    # Check and handle referral
    if referred_by:
        referrer = db.query(Users).filter(Users.id == referred_by).first()
        if not referrer:
            raise HTTPException(status_code=404, detail="Referrer not found")
        referrer.smartstory_credits += 1

    # Commit transaction
    db.commit()
    return {"message": "Subscription registered successfully"}


async def grant_smartstory_credit(user_id: int, db: Session, credits: int):
    user = db.query(Users).filter(Users.id == user_id).first()
    if not user:
        raise HTTPException(status_code=404, detail="User not found")
    user.smartstory_credits += credits
    db.add(user)
    db.commit()
    return user.smartstory_credits

"""

from __future__ import annotations

import os
from uuid import UUID
import datetime
import random
from enum import Enum, unique

from dataclasses import dataclass, field
from typing import ClassVar, Mapping, Dict, Any

import stripe
from fastapi import APIRouter, Depends, Query, HTTPException, Body, Request
from fastapi.templating import Jinja2Templates
from sqlalchemy import select, update
from sqlalchemy.ext.asyncio import AsyncSession

from endpoints.core.config import AppConfig
from endpoints.core.config_loader import ConfigLoader
from endpoints.models.sales_smartstory_plans import SalesSmartStoryPlan
from endpoints.models.sales_smartstory_subscriptions import SalesSmartStorySubscription
from endpoints.models.schemas import SalesSmartStorySubscriptionCreate
from endpoints.models.users import User
from endpoints.util.database import DatabaseSessionManager
from endpoints.models.payment_transactions import PaymentTransaction, PaymentStatus
import logging

from endpoints.auth.auth_service import AuthService # Need AuthService for type hint if not already imported

logger = logging.getLogger(__name__)

# Initialize Jinja templates once (avoids ForwardRef issues with Depends)
templates = Jinja2Templates(directory="endpoints/templates")

payment_router = APIRouter(prefix="/payment", tags=["payment"])

# Router for PUBLIC payment endpoints (webhooks, redirects - NO auth)

public_payment_router = APIRouter(prefix="/payment", tags=["payment-public"])


frontend_url = ConfigLoader().get_frontend_url()
FRONTEND_HOME =  f"{frontend_url}/home"
backend_base_url = ConfigLoader().get_backend_base_url()
support_email = ConfigLoader().get_support_email()
stripe_signature_header_key = ConfigLoader().get_stripe_signature_header_key()

logger.info(f"Using backend base URL for redirects: {backend_base_url}")

def generate_order_id(purchase_type: str):
    """Generate a unique order ID for a purchase."""
    purchase_type = purchase_type.upper()
    order_prefix = "" if "SUBSCRIPTION" in purchase_type else "-" + purchase_type
    order_prefix = f"PI{order_prefix}"
    timestamp = datetime.datetime.now(datetime.timezone.utc).strftime("%Y%m%d%H%M%S")
    random_suffix = "".join(random.choices("ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789", k=6))
    order_id = f"{order_prefix}-{timestamp}-{random_suffix}"

    return order_id

@unique
class StripeEvent(str, Enum):
    CHECKOUT_SESSION_COMPLETED = "checkout.session.completed"
    INVOICE_PAYMENT_SUCCEEDED = "invoice.payment_succeeded"
    INVOICE_PAYMENT_FAILED = "invoice.payment_failed"
    CUSTOMER_SUBSCRIPTION_UPDATED = "customer.subscription.updated"
    CUSTOMER_SUBSCRIPTION_DELETED = "customer.subscription.deleted"
    PAYMENT_INTENT_SUCCEEDED = "payment_intent.succeeded"
    PAYMENT_INTENT_FAILED = "payment_intent.payment_failed"

async def _ensure_stripe_customer(user_obj: User, db: AsyncSession) -> str:
    """
    Ensures the user has a Stripe customer ID, creating one if necessary.

    Args:
        user_obj: The SQLAlchemy User object.
        db: The AsyncSession instance.

    Returns:
        The Stripe customer ID.

    Raises:
        HTTPException: If customer creation fails or user object is invalid.
    """
    if user_obj.stripe_customer_id:
        logger.debug(f"User {user_obj.user_id} already has Stripe customer ID: {user_obj.stripe_customer_id}")
        return user_obj.stripe_customer_id

    logger.info(f"Creating Stripe customer for user {user_obj.user_id}")
    try:
        customer = stripe.Customer.create(
            email=user_obj.auth_email,
            name=f"{user_obj.first_name} {user_obj.last_name}".strip() or None,
            metadata={
                "user_id": str(user_obj.user_id),
                "company": user_obj.company_name or ""
            }
        )
        user_obj.stripe_customer_id = customer.id
        # Add the user object to the session *before* commit if it wasn't already loaded
        # in a way that attached it to this session. If it's already attached, this is harmless.
        db.add(user_obj)
        await db.commit()
        await db.refresh(user_obj) # Refresh to ensure the session has the updated ID
        logger.info(f"Stripe customer created: {customer.id} for user {user_obj.user_id}")
        return customer.id
    except stripe.error.StripeError as e:
        await db.rollback()
        logger.error(f"Stripe error creating customer for user {user_obj.user_id}: {str(e)}")
        # Use the user-facing message if available, otherwise the generic error
        detail = f"Could not create payment profile: {e.user_message or str(e)}"
        raise HTTPException(status_code=500, detail=detail)
    except Exception as e:
        await db.rollback()
        logger.error(f"Unexpected error creating Stripe customer for user {user_obj.user_id}: {str(e)}", exc_info=True)
        raise HTTPException(status_code=500, detail="An unexpected error occurred while setting up payment profile.")




PLAN_TIERS = {
    # price_id: (plan_name, tier_level)
    "price_1R2xcy02BL0Rwhwj77BxxjRz": ("Power", 1),
    "price_1R2xgW02BL0RwhwjiOQOJHy7": ("Growth", 2),
    # Add free plan info if it has a price_id or handle it separately
    # "price_free_plan_id": ("Free", 0)
}

# Mapping for easier lookup by name
PLAN_NAME_TO_TIER = {
    "free": 0,
    "power": 1,
    "growth": 2,
}

PLANS = {
    "price_1R2xcy02BL0Rwhwj77BxxjRz": {
        "name": "Power",
        "tier": 1,
        "uuid": "550e8400-e29b-41d4-a716-************"
    },
    "price_1R2xgW02BL0RwhwjiOQOJHy7": {
        "name": "Growth",
        "tier": 2,
        "uuid": "6ba7b810-9dad-11d1-80b4-00c04fd430c8"
    },
    "free": {
        "name": "Free",
        "tier": 0,
        "uuid": None
    }
}


plan_map = {
    "price_1R2xcy02BL0Rwhwj77BxxjRz": ("Power", "550e8400-e29b-41d4-a716-************"),
    "price_1R2xgW02BL0RwhwjiOQOJHy7": ("Growth", "6ba7b810-9dad-11d1-80b4-00c04fd430c8"),
}


@dataclass(frozen=True, slots=True)
class Plan:
    name: str
    id: str                  # Internal UUID
    price_id: str            # Stripe price ID for the subscription itself
    tier: int
    addon_price_ids: Mapping[str, str] = field(default_factory=dict)


class PlanReference:
    """Central registry of every subscription plan and its add-on packs."""

    # ── Core plans ──────────────────────────────────────────────────────
    free:  ClassVar[Plan]
    power: ClassVar[Plan]
    growth: ClassVar[Plan]

    # ── Internal lookup tables (auto-populated at import) ───────────────
    _stripe_price_map: ClassVar[dict[str, Plan]] = {}
    _code_map:         ClassVar[dict[str, Plan]] = {}
    _lookup:           ClassVar[dict[str, Plan]] = {}

    # ── Public helpers ──────────────────────────────────────────────────
    @classmethod
    def get_by_code(cls, code: str) -> Plan | None:
        return cls._code_map.get(code)

    @classmethod
    def get_by_price_id(cls, price_id: str) -> Plan | None:
        """Accepts either a subscription price ID or an add-on price ID."""
        return cls._stripe_price_map.get(price_id)

    @classmethod
    def get(cls, key: str) -> Plan | None:
        """Accepts codes or any Stripe price ID."""
        return cls._lookup.get(key)


# ── Instantiate the plans
PlanReference.free = Plan(
    name="Free",
    id="f47ac10b-58cc-4372-a567-0e02b2c3d479",
    price_id="",           # Free has no recurring price
    tier=0,
    addon_price_ids={
        # Example: let free users buy the smallest Power pack
        # "Power Pack – 20 SmartStories": "price_REPLACE_ME_FREE_POWER_20",
    },
)

PlanReference.power = Plan(
    name="Power",
    id="550e8400-e29b-41d4-a716-************",
    price_id="price_1R2xcy02BL0Rwhwj77BxxjRz",
    tier=1,
    addon_price_ids={
        "Power Pack – 10 SmartStories": "price_1RGOJy02BL0RwhwjVRUOTS0T", #prod_SAjnaCLfwy2OTm
        "Power Pack – 20 SmartStories": "price_1RGOKO02BL0RwhwjtrjPWWgA", #prod_SAjo9HyBky2gNp
        "Power Pack – 60 SmartStories": "price_1RGOKj02BL0RwhwjRyZ8iQ7u", #prod_SAjoB6qgBTavMI
        "Power Pack – 240 SmartStories": "price_1RGOL602BL0RwhwjjGbXblJj", #prod_SAjo9ajXMFQdcs
    },
)

PlanReference.growth = Plan(
    name="Growth",
    id="6ba7b810-9dad-11d1-80b4-00c04fd430c8",
    price_id="price_1R2xgW02BL0RwhwjiOQOJHy7",
    tier=2,
    addon_price_ids={
        "Growth Pack – 10 SmartStories": "price_1RGOGa02BL0RwhwjBC5yWHqD", #prod_SAjktTxBqslTX8
        "Growth Pack – 20 SmartStories":  "price_1RGOHi02BL0Rwhwj69AyqfJv", #prod_SAjlkC7oPhzUho",
        "Growth Pack – 60 SmartStories": "price_1RGOIY02BL0RwhwjgT98xvzp", #prod_SAjmeTVxvKjxPN,
        "Growth Pack – 240 SmartStories": "price_1RGOJF02BL0RwhwjzOilAZxo", #prod_SAjmDl39STsIKO
    },
)

# ── Build lookup tables (subscription + add-ons) ───────────────────────
for plan in (PlanReference.free, PlanReference.power, PlanReference.growth):
    # Code map (same naming you already use)
    PlanReference._code_map[plan.name.upper()] = plan

    # Subscription price-id → plan
    if plan.price_id:
        PlanReference._stripe_price_map[plan.price_id] = plan

    # Add-on price-id → plan
    PlanReference._stripe_price_map.update(plan.addon_price_ids.values() and
        {pid: plan for pid in plan.addon_price_ids.values()} or {})


PlanReference._lookup = {
    **PlanReference._code_map,
    **PlanReference._stripe_price_map,
}


# @payment_router.get("/")
# async def index(
#     request: Request,
#     user_id: UUID = Query(..., description="User ID to check if user is a Stripe customer"),
#     db: AsyncSession = Depends(DatabaseSessionManager.get_session),
# ):
#     """Landing page – shows subscription, management, and add-on packs."""
#
#     logger.info(f"Payment management page requested for user {user_id}")
#
#     result = await db.execute(
#         select(User).where(User.user_id == user_id)
#     )
#     user_obj: User | None = result.scalar_one_or_none()
#
#     if user_obj is None:
#         raise HTTPException(status_code=404, detail="User not found")
#
#     # Get current subscription details with plan description
#     subscription_result = await db.execute(
#         select(SalesSmartStorySubscription, SalesSmartStoryPlan.plan_desc)
#         .join(SalesSmartStoryPlan, SalesSmartStorySubscription.plan_id == SalesSmartStoryPlan.plan_id)
#         .filter(
#             SalesSmartStorySubscription.user_id == user_id,
#             SalesSmartStorySubscription.active_subscription == True
#         )
#     )
#     subscription_data = subscription_result.first()
#
#     has_customer = bool(user_obj.stripe_customer_id)
#     current_plan = subscription_data.plan_desc if subscription_data else None
#
#     # Get user name information
#     first_name = user_obj.first_name or ""
#     last_name = user_obj.last_name or ""
#     user_name = f"{first_name} {last_name}".strip().title()
#     first_initial = first_name[0].upper() if first_name else "U"
#     user_email = user_obj.auth_email or "No Email"
#
#
#     return templates.TemplateResponse(
#         "index.html",
#         {
#             "request": request,
#             "hasCustomer": has_customer,
#             "user_id": str(user_id),
#             "current_plan": current_plan,
#             "user_name": user_name,
#             "first_initial": first_initial,
#             "user_email": user_email,
#             "frontend_url": frontend_url
#         },
#     )


@public_payment_router.get("/success")
async def success(
    request: Request,
    session_id: str = Query(None, alias="session_id"),
    user_id: str = Query(None, alias="user_id"),
    plan: str = Query(None, alias="plan"),
    db: AsyncSession = Depends(DatabaseSessionManager.get_session)
):
    """Stripe Checkout success redirect with status check and correct Order ID."""

    order_id: str | None = None
    processing = False
    completed = False
    plan_name: str | None = None

    if session_id:
        logger.info(f"Success page requested for session_id: {session_id}")
        transaction_query = await db.execute(
            select(PaymentTransaction)
            .where(PaymentTransaction.stripe_session_id == session_id)
        )
        transaction = transaction_query.scalar_one_or_none()

        if transaction:
            order_id = transaction.order_id
            plan_name = transaction.plan_name

            logger.info(f"Found transaction with order_id: {order_id} for session {session_id}")

            if transaction.status == PaymentStatus.completed.value:
                completed = True
                logger.info(f"Transaction {order_id} already completed.")
            elif transaction.status == PaymentStatus.pending.value:
                processing = True
                logger.info(f"Transaction {order_id} is pending.")

                recovery_threshold = datetime.datetime.now(datetime.timezone.utc) - datetime.timedelta(minutes=5)
                if transaction.created_at and isinstance(transaction.created_at, datetime.datetime) and transaction.created_at < recovery_threshold:
                    logger.warning(f"Transaction {order_id} pending for > 5 mins. Attempting recovery.")
                    try:
                        stripe_session = stripe.checkout.Session.retrieve(session_id)
                        if stripe_session.status == 'complete' and not transaction.webhook_received_at:
                            logger.info(f"Stripe session {session_id} is complete. Manually processing success for {order_id}.")
                            await process_checkout_success(stripe_session, transaction, db)
                            completed = True
                            processing = False
                            logger.info(f"Recovery successful for {order_id}.")
                        elif transaction.webhook_received_at:
                             logger.info(f"Recovery not needed for {order_id}, webhook already received.")
                        else:
                             logger.info(f"Recovery check for {order_id}: Stripe session status is '{stripe_session.status}'.")
                    except stripe.error.StripeError as e:
                         logger.error(f"Stripe API error during recovery attempt for {order_id}: {str(e)}")
                    except Exception as e:
                        logger.error(f"Recovery attempt failed for transaction {order_id}: {str(e)}", exc_info=True)
                else:
                    logger.info(f"Recovery check for {order_id}: Stripe session status is '{stripe_session.status}'.")
        else:
            logger.warning(f"No transaction found for session_id: {session_id}. Cannot display specific order ID.")
            plan_name = plan

    else:
        logger.warning("Success page accessed without session_id.")
        plan_name = plan

    plan_name = plan_name or "Subscription"

    current_date = datetime.datetime.now().strftime('%Y-%m-%d')


    return templates.TemplateResponse(
        "success.html",
        {
            "request": request,
            "session_id": session_id,
            "order_id": order_id,
            "date": current_date,
            "plan": plan_name,
            "user_id": user_id,
            "processing": processing,
            "completed": completed,
            "frontend_url": frontend_url
        }
    )


@public_payment_router.get("/cancel")
async def cancel(request: Request):
    """Stripe Checkout cancel redirect."""
    # Get user_id directly from query parameters if provided
    user_id = request.query_params.get("user_id")

    # Validate that it's a proper UUID if present
    if user_id:
        try:
            # Attempt to convert to UUID to validate format
            uuid_obj = UUID(user_id)
            # Convert back to string in normalized form
            user_id = str(uuid_obj)
        except ValueError:
            # If invalid UUID format, don't use it
            user_id = None

    return templates.TemplateResponse(
        "cancel.html",
        {
            "request": request,
            "support_email": support_email,
            "user_id": user_id,  # Only pass validated user_id,
            "frontend_url": frontend_url,
        },
    )


@payment_router.post("/create-checkout-session")
async def create_checkout_session(
    price_id: str = Body(...),
    user_id: UUID = Body(...),
    db: AsyncSession = Depends(DatabaseSessionManager.get_session),
):
    """Create a Stripe Checkout Session for a subscription purchase, enforcing tier logic."""
    logger.info(f"Received request to create checkout session for user {user_id} with price_id {price_id}")
    # --- Target Plan Identification ---
    target_plan_info = PLAN_TIERS.get(price_id)
    if not target_plan_info:
        logger.error(f"Invalid price_id received: {price_id}")
        raise HTTPException(status_code=400, detail="Invalid price ID provided.")
    target_plan_name, target_plan_tier = target_plan_info
    logger.info(f"Target plan identified: {target_plan_name} (Tier {target_plan_tier})")

    try:
        # --- Get User ---
        user_result = await db.execute(select(User).where(User.user_id == user_id))
        user_obj: User | None = user_result.scalar_one_or_none()

        if not user_obj:
            logger.error(f"User not found: {user_id}")
            raise HTTPException(status_code=404, detail="User not found")

        # --- Get Current Active Subscription ---
        current_plan_name = "free" # Default to free if no active sub
        current_plan_tier = 0

        sub_result = await db.execute(
            select(SalesSmartStorySubscription, SalesSmartStoryPlan.plan_desc)
            .join(SalesSmartStoryPlan, SalesSmartStorySubscription.plan_id == SalesSmartStoryPlan.plan_id)
            .where(
                SalesSmartStorySubscription.user_id == user_id,
                SalesSmartStorySubscription.active_subscription == True # Ensure we only check active subs
            )
            .order_by(SalesSmartStorySubscription.created_at.desc()) # Get the latest active one if multiple somehow exist
        )
        active_sub_data = sub_result.first()

        if active_sub_data:
            current_subscription, current_plan_desc = active_sub_data
            current_plan_name = current_plan_desc.lower() if current_plan_desc else "unknown"
            current_plan_tier = PLAN_NAME_TO_TIER.get(current_plan_name, -1) # Use -1 for unknown
            logger.info(f"User {user_id} has active subscription: {current_plan_name} (Tier {current_plan_tier})")
        else:
            logger.info(f"User {user_id} has no active subscription (considered Free Tier 0).")


        # --- Enforce Subscription Logic ---
        if current_plan_tier == target_plan_tier:
             detail = f"You are already subscribed to the {target_plan_name} plan."
             logger.warning(f"Blocked session creation for user {user_id}: Already on target plan {target_plan_name}.")
             raise HTTPException(status_code=409, detail=detail) # 409 Conflict

        if current_plan_tier > target_plan_tier:
            # Block downgrades via this endpoint
            detail = f"Downgrades from {current_plan_name.capitalize()} to {target_plan_name} must be handled via the Billing Portal or support."
            logger.warning(f"Blocked session creation for user {user_id}: Attempted downgrade from {current_plan_name} to {target_plan_name}.")
            raise HTTPException(status_code=400, detail=detail) # 400 Bad Request

        # If current_plan_tier < target_plan_tier, it's an upgrade or first subscription (from free), which is allowed.

        # --- Proceed with Checkout Session Creation ---
        logger.info(f"Subscription logic passed for user {user_id}. Proceeding to create checkout session.")

        # Get the target plan_id from the database based on target_plan_name
        plan_result = await db.execute(
            select(SalesSmartStoryPlan.plan_id)
            .where(SalesSmartStoryPlan.plan_desc == target_plan_name.lower())
        )
        target_db_plan_id = plan_result.scalar_one_or_none()
        if not target_db_plan_id:
            logger.error(f"Database lookup failed for plan_id with name: {target_plan_name}")
            # This case indicates an inconsistency between PLAN_TIERS and the DB
            raise HTTPException(status_code=500, detail="Internal configuration error finding plan.")

        user_obj.stripe_customer_id = await _ensure_stripe_customer(user_obj, db)
        await db.commit()
        logger.info(f"Stripe customer created: {user_obj.stripe_customer_id}")

        # Generate a properly formatted order ID
        # timestamp = datetime.datetime.now(datetime.timezone.utc).strftime('%Y%m%d%H%M%S')
        # random_suffix = ''.join(random.choices('ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789', k=6))
        # order_id = f"PI-{timestamp}-{random_suffix}"
        order_id = generate_order_id("Subscription")
        logger.info(f"Generated Order ID: {order_id}")

        # Create the checkout session
        logger.info(f"Creating Stripe Checkout session for user {user_id}, plan {target_plan_name}")
        checkout_session = stripe.checkout.Session.create(
            customer=user_obj.stripe_customer_id,
            success_url=f"{backend_base_url}/payment/success?session_id={{CHECKOUT_SESSION_ID}}",
            cancel_url=f"{backend_base_url}/payment/cancel?user_id={user_id}",
            payment_method_types=["card"],
            mode="subscription",
            line_items=[{"price": price_id, "quantity": 1}],
            metadata={
                "user_id": str(user_id),
                "plan": target_plan_name,
                "order_id": order_id
            }
        )
        logger.info(f"Stripe Checkout session created: {checkout_session.id}")

        # Create transaction record
        transaction = PaymentTransaction(
            order_id=order_id,
            user_id=user_id,
            stripe_session_id=checkout_session.id,
            stripe_customer_id=user_obj.stripe_customer_id,
            amount_cents=None, # Will be updated by webhook
            currency="USD", # Assuming USD
            plan_id=target_db_plan_id, # Use the looked-up plan ID
            plan_name=target_plan_name,
            status=PaymentStatus.pending.value,
            transaction_metadata={
                "price_id": price_id,
                "checkout_session_id": checkout_session.id
            }
        )

        db.add(transaction)
        await db.commit()
        logger.info(f"Pending PaymentTransaction {transaction.transaction_id} created for order {order_id}")

        # Return the FULL URL
        return {"sessionId": checkout_session.id, "url": checkout_session.url}

    except stripe.error.StripeError as e:
        await db.rollback()
        logger.error(f"Stripe error during checkout session creation for user {user_id}: {str(e)}")
        raise HTTPException(status_code=400, detail=f"Stripe Error: {e.user_message or str(e)}")
    except HTTPException as e:
        # Re-raise HTTPExceptions raised intentionally (like 409, 404)
        raise e
    except Exception as e:
        await db.rollback()
        logger.error(f"Unexpected error during checkout session creation for user {user_id}: {str(e)}", exc_info=True)
        raise HTTPException(status_code=500, detail="An unexpected error occurred.")


@payment_router.post("/create-portal-session")
async def create_portal_session(
    user_id: UUID = Body(..., embed=True),
    db: AsyncSession = Depends(DatabaseSessionManager.get_session),
):
    """Generate a Stripe Billing Portal session so the user can manage their subscription."""
    result = await db.execute(select(User).where(User.user_id == user_id))
    user_obj: User | None = result.scalar_one_or_none()

    if not user_obj or not user_obj.stripe_customer_id:
        raise HTTPException(status_code=400, detail="User is not a valid Stripe customer")

    try:
        logger.info(f"Creating Stripe billing portal session for user {user_id}")
        session = stripe.billing_portal.Session.create(
            customer=user_obj.stripe_customer_id,
            return_url=FRONTEND_HOME,
        )
        logger.info(f"Stripe portal session created: {session.id} for user {user_id}")
        return {"url": session.url}
    except stripe.error.StripeError as e:
        logger.error(f"Stripe error creating portal session for user {user_id}: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Could not create billing portal session: {e.user_message or str(e)}")
    except Exception as e:
        logger.error(f"Unexpected error creating portal session for user {user_id}: {str(e)}")
        raise HTTPException(status_code=500, detail="An unexpected error occurred.")


# @payment_router.post("/create-customer")
# async def create_stripe_customer(
#     user_id: UUID = Body(..., embed=True),
#     db: AsyncSession = Depends(DatabaseSessionManager.get_session),
# ):
#     """Create a Stripe customer for a user if they don't have one."""
#     # Get user from database
#     result = await db.execute(select(User).where(User.user_id == user_id))
#     user: User | None = result.scalar_one_or_none()
#
#     if not user:
#         raise HTTPException(status_code=404, detail="User not found")
#
#     if user.stripe_customer_id:
#         return {"customer_id": user.stripe_customer_id}
#
#     try:
#         # Create a new Stripe customer
#         customer = stripe.Customer.create(
#             email=user.auth_email,
#             name=f"{user.first_name} {user.last_name}".strip() or None,
#             metadata={
#                 "user_id": str(user.user_id),
#                 "company": user.company_name or ""
#             }
#         )
#
#         # Update user with Stripe customer ID
#         user.stripe_customer_id = customer.id
#         await db.commit()
#
#         return {"customer_id": customer.id}
#
#     except stripe.error.StripeError as e:
#         await db.rollback()
#         raise HTTPException(status_code=400, detail=str(e))


@public_payment_router.post("/webhook")
async def stripe_webhook(request: Request, db: AsyncSession = Depends(DatabaseSessionManager.get_session)):
    """Handle Stripe webhook events for payment and subscription lifecycle."""
    payload = await request.body()

    sig_header = request.headers.get(stripe_signature_header_key)
    endpoint_secret = os.getenv("STRIPE_WEBHOOK_SECRET")

    try:
        # Verify webhook signature
        event = stripe.Webhook.construct_event(
            payload, sig_header, endpoint_secret
        )
    except ValueError:
        logger.error("Invalid payload in Stripe webhook")
        raise HTTPException(status_code=400, detail="Invalid payload")
    except stripe.error.SignatureVerificationError:
        logger.error("Invalid signature in Stripe webhook")
        raise HTTPException(status_code=400, detail="Invalid signature")

    # Check for idempotency - have we processed this event before?
    event_id = event.id
    transaction_query = await db.execute(
        select(PaymentTransaction)
        .where(PaymentTransaction.stripe_event_id == event_id)
    )
    if transaction_query.scalar_one_or_none():
        logger.info(f"Stripe event {event_id} already processed, skipping")
        return {"status": "success"}

    event_type = event['type']
    logger.info(f"Processing Stripe webhook event: {event_type} ({event_id})")

    try:
        # CHECKOUT SESSION COMPLETED
        # Triggered when customer completes checkout
        if event_type == StripeEvent.CHECKOUT_SESSION_COMPLETED:
            await handle_checkout_session_completed(event, db)

        # INVOICE PAYMENT SUCCEEDED
        # Triggered for subscription renewals and initial subscription payments
        elif event_type == StripeEvent.INVOICE_PAYMENT_SUCCEEDED:
            await handle_invoice_payment_succeeded(event, db)

        # INVOICE PAYMENT FAILED
        # Triggered when a subscription payment fails
        elif event_type == StripeEvent.INVOICE_PAYMENT_FAILED:
            await handle_invoice_payment_failed(event, db)

        # CUSTOMER SUBSCRIPTION UPDATED
        # Triggered when subscription details change (upgrade, downgrade, quantity change)
        elif event_type == StripeEvent.CUSTOMER_SUBSCRIPTION_UPDATED:
            await handle_subscription_updated(event, db)

        # CUSTOMER SUBSCRIPTION DELETED
        # Triggered when subscription is canceled
        elif event_type == StripeEvent.CUSTOMER_SUBSCRIPTION_DELETED:
            await handle_subscription_deleted(event, db)

        # PAYMENT INTENT SUCCEEDED
        # For one-time payments outside of subscriptions
        elif event_type == StripeEvent.PAYMENT_INTENT_SUCCEEDED:
            await handle_payment_intent_succeeded(event, db)

        # PAYMENT INTENT FAILED
        # For failed one-time payments
        elif event_type == StripeEvent.PAYMENT_INTENT_FAILED:
            await handle_payment_intent_failed(event, db)

        else:
            logger.info(f"Unhandled Stripe event type: {event_type}")

        return {"status": "success"}

    except Exception as e:
        logger.error(f"Error processing Stripe webhook {event_type}: {str(e)}", exc_info=True)
        # Return 200 to acknowledge receipt to Stripe
        # (Stripe will retry otherwise, which we don't want for already processed but failed events)
        return {"status": "error handled"}


async def process_checkout_success(session, transaction, db):
    """Shared function to process successful checkout."""


    try:
        # Get user details
        user_id = transaction.user_id

        if not user_id:
            logger.error(f"Missing user_id for transaction {transaction.transaction_id}")
            transaction.error_message = "Missing user_id"
            await db.commit()
            return False

        # Determine if this is an add-on purchase or subscription
        is_addon = transaction.plan_id is None or "addon" in (transaction.transaction_metadata or {}).get("purchase_type", "").lower()

        if is_addon:
            # For add-on purchases, we don't need to create a subscription
            logger.info(f"Processing add-on purchase for user {user_id}")

            # Get user to update available credits if needed
            user_query = await db.execute(select(User).where(User.user_id == user_id))
            user = user_query.scalar_one_or_none()

            if user:
                # For add-ons, find the active subscription to add credits
                sub_query = await db.execute(
                    select(SalesSmartStorySubscription)
                    .filter(
                        SalesSmartStorySubscription.user_id == user_id,
                        SalesSmartStorySubscription.active_subscription == True
                    )
                )
                active_sub = sub_query.scalar_one_or_none()

                if active_sub:
                    # Extract credit amount from the product name if possible
                    product_name = transaction.plan_name or ""
                    # Try to parse number of credits, fallback to a reasonable default (e.g., 10)
                    try:
                        # Example format: "Power Pack – 10 SmartStories"
                        credit_parts = product_name.split("–")
                        if len(credit_parts) > 1:
                            credits_text = credit_parts[1].strip().split(" ")[0]
                            additional_credits = int(credits_text)
                        else:
                            additional_credits = 10  # Default fallback
                    except (ValueError, IndexError):
                        additional_credits = 10  # Default fallback

                    # Add credits to the user's subscription
                    active_sub.available_credit = active_sub.available_credit + additional_credits
                    logger.info(f"Added {additional_credits} credits to user {user_id}")
                else:
                    logger.warning(f"No active subscription found for user {user_id} to add credits to")
            else:
                logger.error(f"User not found for transaction {transaction.transaction_id}")
        else:
            # Original subscription flow
            plan_id = transaction.plan_id
            if not plan_id:
                logger.error(f"Missing plan_id for subscription transaction {transaction.transaction_id}")
                transaction.error_message = "Missing plan_id for subscription"
                await db.commit()
                return False

            sub_start_date, sub_end_date = None, None
            stripe_subscription_id_from_session = session.get('subscription')

            if stripe_subscription_id_from_session:
                try:
                    stripe_sub_object = stripe.Subscription.retrieve(stripe_subscription_id_from_session)
                    sub_start_date, sub_end_date = get_billing_period_from_subscription_retrieve(subscription= stripe_sub_object)

                    logger.info(f"For new subscription via checkout session {session.id}, fetched billing dates: Start={sub_start_date}, End={sub_end_date}")
                except stripe.error.StripeError as e:
                    logger.error(f"Stripe error fetching subscription {stripe_subscription_id_from_session} details in process_checkout_success: {e}")
                    # Decide if you want to proceed with None dates or fail
                    # For a new paid subscription, failing if dates can't be fetched might be safer.
                    # For now, will proceed with None if fetch fails, but this is a risk.
                except Exception as e:
                    logger.error(f"Unexpected error fetching subscription {stripe_subscription_id_from_session} details: {e}", exc_info=True)



            # Create subscription data model
            subscription_data = SalesSmartStorySubscriptionCreate(
                user_id=user_id,
                plan_id=plan_id,
                transaction_id=transaction.transaction_id,
                transaction_date=datetime.datetime.now().date(),
                current_period_start=sub_start_date,
                current_period_end=sub_end_date
            )
            # Process subscription
            from endpoints.routers.subscription_routers.save_subscription import create_subscription
            await create_subscription(subscription_data, db)

        # Update transaction record in both cases
        transaction.status = PaymentStatus.completed.value
        transaction.webhook_received_at = transaction.webhook_received_at or datetime.datetime.now()

        # Set fields based on session data
        subscription_id = session.get("subscription")
        if subscription_id:
            transaction.stripe_subscription_id = subscription_id

        await db.commit()
        logger.info(f"Successfully processed {'add-on' if is_addon else 'subscription'} checkout for user {user_id}")
        return True

    except Exception as e:
        logger.error(f"Error in process_checkout_success: {str(e)}", exc_info=True)
        await db.rollback()
        # Still mark the error
        transaction.error_message = str(e)
        await db.commit()
        return False


async def handle_checkout_session_completed(event, db):
    """Process successful checkout completion via webhook."""
    session = event['data']['object']


    # Find the transaction using session ID
    transaction_query = await db.execute(
        select(PaymentTransaction)
        .where(PaymentTransaction.stripe_session_id == session.id)
    )
    transaction = transaction_query.scalar_one_or_none()

    if not transaction:
        logger.error(f"No transaction found for checkout session {session.id}")
        return

    # Mark this event as processed
    transaction.stripe_event_id = event.id

    # If already completed, just update the event ID and return
    if transaction.status == PaymentStatus.completed.value:
        await db.commit()
        return

    # Process the checkout success
    success = await process_checkout_success(session, transaction, db)

    # If success, update user status
    if success:
        try:
            # Find user by user_id
            user_query = await db.execute(
                select(User).where(User.user_id == transaction.user_id)
            )
            user = user_query.scalar_one_or_none()

            if user:
                # Determine if this is an add-on purchase
                is_addon = transaction.plan_id is None or "addon" in (transaction.transaction_metadata or {}).get("purchase_type", "").lower()

                if not is_addon:
                    # Only update user's plan_id for subscription purchases, not add-ons
                    # Update user provisioning status
                    user.is_user_provisioned = True

                    # Update paid status based on plan name/id
                    if transaction.plan_name and transaction.plan_name.lower() != 'free':
                        user.is_paid_user = True

                    # Update user's plan_id ONLY for subscriptions
                    if transaction.plan_id:
                        user.plan_id = transaction.plan_id
                else:
                    # For add-ons, just log that we're not modifying plan_id
                    logger.info(f"Add-on purchase: not updating plan_id for user {user.user_id}")

                # Commit the changes
                await db.commit()
                logger.info(f"Updated user {user.user_id} status after {'add-on' if is_addon else 'subscription'} purchase")
            else:
                logger.error(f"User not found for transaction {transaction.transaction_id}")
        except Exception as e:
            logger.error(f"Failed to update user status: {str(e)}", exc_info=True)
            # Don't rollback the transaction processing, just log the error


async def handle_invoice_payment_succeeded(event, db):
    """Process successful subscription renewal payments."""
    invoice = event['data']['object']


    plan_id = None
    user = None # Initialize user variable

    # Only process subscription invoices
    subscription_id = invoice.get('subscription')
    if not subscription_id:
        logger.info(f"Invoice {invoice.id} is not for a subscription, skipping")
        return

    # Find the customer
    customer_id = invoice.get('customer')
    if not customer_id:
        logger.error(f"No customer found for invoice {invoice.id}")
        return

    # Fetch user earlier
    try:
        user_query = await db.execute(select(User).where(User.stripe_customer_id == customer_id))
        user = user_query.scalar_one_or_none()
        if not user:
            logger.error(f"No user found for Stripe customer {customer_id}")
            return # Cannot proceed without a user
    except Exception as user_fetch_err:
         logger.error(f"Error fetching user for customer {customer_id}: {user_fetch_err}", exc_info=True)
         # Depending on policy, might need rollback if using same session, but likely safe to just return
         return

    # Process transaction and subscription
    transaction = None # Initialize
    try:
        # Check if we already have a transaction for this invoice
        transaction_query = await db.execute(
            select(PaymentTransaction)
            .where(PaymentTransaction.stripe_invoice_id == invoice.id)
        )
        transaction = transaction_query.scalar_one_or_none()

        # If no transaction exists, create one for tracking
        if not transaction:
            logger.info(f"Creating new transaction for invoice {invoice.id}")
            # Get subscription details safely
            try:
                retrieved_subscription = stripe.Subscription.retrieve(subscription_id) # Renamed variable

                items_data = get_items_data_from_subscription_retrieve(retrieved_subscription)
                sub_start_date, sub_end_date = get_billing_period_from_subscription_retrieve(retrieved_subscription)


                print(f"Subscription start date: {sub_start_date}, end date: {sub_end_date}")
                # -----------------------------------------

                if items_data:
                    price_id = items_data[0].get('price', {}).get('id')
                    plan_info_ref = PlanReference.get_by_price_id(price_id)
                    if plan_info_ref:
                        plan_name = plan_info_ref.name
                        plan_id = UUID(plan_info_ref.id)
                    else:
                        logger.warning(f"Unknown price_id {price_id} on renewal for subscription {subscription_id}")
                        plan_name = "Unknown Plan"
                        plan_id = None
                else:
                    logger.error(f"No items found in subscription {subscription_id} during renewal")
                    plan_name = "Error - No Items"
                    plan_id = None

            except stripe.error.StripeError as stripe_err:
                 logger.error(f"Stripe API error retrieving subscription {subscription_id}: {stripe_err}", exc_info=True)
                 plan_name = "Error - Stripe API"
                 plan_id = None
                 sub_start_date = None
                 sub_end_date = None
            except Exception as detail_err:
                 logger.error(f"Unexpected error retrieving subscription details: {detail_err}", exc_info=True)
                 plan_name = "Error - Fetching Details"
                 plan_id = None
                 sub_start_date = None
                 sub_end_date = None

            # # Generate order ID
            # timestamp = datetime.datetime.now(datetime.timezone.utc).strftime('%Y%m%d%H%M%S')
            # random_suffix = ''.join(random.choices('ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789', k=6))
            # order_id = f"PI-{timestamp}-{random_suffix}"
            order_id = generate_order_id("Subscription")

            # Create transaction record
            transaction = PaymentTransaction(
                order_id=order_id,
                user_id=user.user_id,
                stripe_customer_id=customer_id,
                stripe_subscription_id=subscription_id,
                stripe_invoice_id=invoice.id,
                stripe_payment_intent_id=invoice.payment_intent,
                stripe_event_id=event.id,
                amount_cents=invoice.amount_paid,
                currency=invoice.currency.upper(),
                plan_id=plan_id, # Use the determined plan_id (could be None)
                plan_name=plan_name,
                status=PaymentStatus.completed.value,
                transaction_metadata={"invoice_number": invoice.number, "is_renewal": True},
                webhook_received_at=datetime.datetime.now(datetime.timezone.utc),
                subscription_start_date=sub_start_date, # Use safely fetched date
                subscription_end_date=sub_end_date    # Use safely fetched date
            )
            db.add(transaction)
            await db.flush([transaction]) # Flush to get ID, catch errors if needed

            # Renew the subscription credits in DB
            try:
                # Find current subscription in DB
                sub_query = await db.execute(
                    select(SalesSmartStorySubscription)
                    .filter(
                        SalesSmartStorySubscription.user_id == user.user_id,
                        SalesSmartStorySubscription.active_subscription == True
                    )
                )
                current_sub = sub_query.scalar_one_or_none()

                if current_sub and plan_id: # Need plan_id to update credits
                    plan_result = await db.execute(
                        select(SalesSmartStoryPlan).filter(SalesSmartStoryPlan.plan_id == plan_id)
                    )
                    plan = plan_result.scalars().first()
                    if plan:
                        current_sub.monthly_subscribed_credit = plan.plan_credit
                        current_sub.available_credit = plan.plan_credit  # Reset credits
                        current_sub.transaction_id = transaction.transaction_id # Link to renewal transaction
                        current_sub.transaction_date = datetime.datetime.now(datetime.timezone.utc).date()
                        current_sub.current_period_start = sub_start_date
                        current_sub.current_period_end = sub_end_date

                        logger.debug('current_sub: current_period_end', current_sub.current_period_end)
                        db.add(current_sub)

                        logger.info(f"Updated credits for subscription {current_sub.subscription_id} on renewal")
                    else:
                        logger.error(f"Could not find plan with ID {plan_id} in DB for renewal credit update")
                elif not current_sub:
                    # This case handles recovery if a renewal invoice arrives but we lack a local active sub record.
                    logger.warning(f"No active DB subscription found for user {user.user_id} during renewal invoice {invoice.id}. Attempting creation.")
                    # Ensure we have all necessary data fetched earlier: plan_id, transaction.transaction_id, sub_start_date, sub_end_date
                    if plan_id and transaction and transaction.transaction_id:
                         # Prepare data for the creation function/schema
                         subscription_data = SalesSmartStorySubscriptionCreate(
                             user_id=user.user_id,
                             plan_id=plan_id,
                             transaction_id=transaction.transaction_id,
                             transaction_date=datetime.datetime.now(datetime.timezone.utc).date(),
                             current_period_start = sub_start_date,
                             current_period_end = sub_end_date
                         )
                         from endpoints.routers.subscription_routers.save_subscription import create_subscription
                         try:

                             # Assume create_subscription handles basic creation and setting credits based on plan_id
                             # and returns the newly created subscription object.
                             await create_subscription(subscription_data, db, carry_over_credits=None) # No carry-over for new sub

                             # if new_sub:
                                # === Explicitly set dates on the newly created subscription ===
                                # new_sub.current_period_start = sub_start_date
                                # new_sub.current_period_end = sub_end_date
                                # new_sub.active_subscription should be True by default in create_subscription? Verify.
                                # If not, set it: new_sub.active_subscription = True
                                # db.add(new_sub) # Add the updated new_sub back to the session
                                # logger.info(f"Created new subscription {new_sub.subscription_id} with billing dates during renewal processing.")
                                # =============================================================
                             # else:
                             #     logger.error("create_subscription returned None, failed recovery.")
                                 # Transaction is already completed, what to do? Log error.
                                 # if transaction:
                                 #     transaction.error_message = "Subscription record creation failed during recovery."
                                 #     db.add(transaction)

                         except Exception as create_ex:
                              logger.error(f"Error calling create_subscription during recovery: {create_ex}", exc_info=True)
                              if transaction:
                                  transaction.error_message = f"Subscription recovery failed: {str(create_ex)}"
                                  db.add(transaction)
                              # Re-raise or handle? Let's re-raise to indicate handler failure
                              raise create_ex
                    else:
                         # Log which piece of data was missing
                         missing_data = []
                         if not plan_id: missing_data.append("plan_id")
                         if not transaction or not transaction.transaction_id: missing_data.append("transaction_id")
                         if not sub_start_date: missing_data.append("sub_start_date")
                         if not sub_end_date: missing_data.append("sub_end_date")
                         logger.error(f"Cannot create subscription on renewal recovery for user {user.user_id} due to missing data: {', '.join(missing_data)}.")
                         if transaction:
                             transaction.error_message = f"Missing required data for subscription recovery: {', '.join(missing_data)}"
                             db.add(transaction)
            except Exception as sub_db_error:
                 logger.error(f"Error updating DB subscription credits/status during renewal: {str(sub_db_error)}", exc_info=True)
                 if transaction: # Add error message to transaction if it exists
                     transaction.error_message = f"DB Subscription update failed: {str(sub_db_error)}"
                 # Continue to user update step, but log the error

        else: # Transaction already exists
            logger.info(f"Transaction {transaction.transaction_id} already exists for invoice {invoice.id}")
            plan_id = transaction.plan_id # Get plan_id from existing transaction

        # Update User Status (outside the transaction creation block)
        if user:
             user.is_user_provisioned = True
             user.is_paid_user = True # Assume success means paid

             if plan_id:
                 user.plan_id = plan_id
             # Log *before* potential commit issues
             logger.info(f"Prepared user {user.user_id} status update for renewal (plan_id: {plan_id})")
             # No db.commit() here, let main handler do it.
        else:
            # This case should have been caught earlier
             logger.error("User object became None before final status update")

    except Exception as e:
        logger.error(f"Error processing invoice payment succeeded for invoice {invoice.id}: {str(e)}", exc_info=True)
        await db.rollback() # Rollback on any error during transaction/subscription processing
        raise e # Re-raise to be caught by the main webhook handler


async def handle_invoice_payment_failed(event, db):
    """Process failed subscription payments."""
    invoice = event['data']['object']


    # Only process subscription invoices
    subscription_id = invoice.get('subscription')
    if not subscription_id:
        return

    # Find the customer
    customer_id = invoice.get('customer')
    if not customer_id:
        logger.error(f"No customer found for invoice {invoice.id}")
        return

    # Find user by Stripe customer ID
    user_query = await db.execute(
        select(User).where(User.stripe_customer_id == customer_id)
    )
    user = user_query.scalar_one_or_none()

    if not user:
        logger.error(f"No user found for Stripe customer {customer_id}")
        return

    try:
        # Generate order ID
        # timestamp = datetime.datetime.now().strftime('%Y%m%d%H%M%S')
        # random_suffix = ''.join(random.choices('ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789', k=6))
        # order_id = f"PI-{timestamp}-{random_suffix}"
        order_id = generate_order_id("Subscription")

        # Record the failed payment
        transaction = PaymentTransaction(
            order_id=order_id,
            user_id=user.user_id,
            stripe_customer_id=customer_id,
            stripe_subscription_id=subscription_id,
            stripe_invoice_id=invoice.id,
            stripe_event_id=event.id,
            status=PaymentStatus.failed.value,
            transaction_metadata={
                "attempt_count": invoice.attempt_count,
                "next_payment_attempt": (
                    datetime.datetime.fromtimestamp(invoice.next_payment_attempt).isoformat()
                    if invoice.next_payment_attempt else None
                )
            },
            error_message=f"Payment failed: {invoice.get('last_payment_error', {}).get('message', 'Unknown error')}",
            webhook_received_at=datetime.datetime.now()
        )
        db.add(transaction)

        # Optional: Mark subscription as past due in our system
        # await send_payment_failed_email(user.user_id, user.auth_email)

        logger.info(f"Recorded failed payment for user {user.user_id}, subscription {subscription_id}")
    except Exception as e:
        logger.error(f"Error processing failed payment: {str(e)}", exc_info=True)
        await db.rollback()


async def handle_subscription_updated(event, db):
    """Handle subscription updates (upgrades, downgrades, etc.)"""
    subscription = event['data']['object']
    previous_attributes = event.get('data', {}).get('previous_attributes', {})
    # Skip if no important changes
    if not previous_attributes:
        return

    # Find the customer
    customer_id = subscription.get('customer')
    if not customer_id:
        logger.error(f"No customer found for subscription {subscription.id}")
        return

    # Find user by Stripe customer ID
    user_query = await db.execute(
        select(User).where(User.stripe_customer_id == customer_id)
    )
    user = user_query.scalar_one_or_none()

    if not user:
        logger.error(f"No user found for Stripe customer {customer_id}")
        return

    try:
        plan_changed = False
        new_plan_id = None
        plan_name = "Unknown Plan"
        existing_credits = 0 # Initialize existing_credits

        # --- FIX: Use dictionary access for subscription items ---
        subscription_items = subscription.get('items', {})
        subscription_data = subscription_items.get('data', [])
        # --------------------------------------------------------

        # Determine the new plan based on current subscription items
        # --- FIX: Use the extracted subscription_data list ---
        if subscription_data:
            current_price_id = subscription_data[0].get('price', {}).get('id')
        # --------------------------------------------------

            if current_price_id: # Check if we actually got a price ID
                plan_info = PlanReference.get(current_price_id)
                if plan_info:
                    plan_name = plan_info.name
                    new_plan_id = UUID(plan_info.id)
                    # --- Check if plan actually changed compared to previous ---
                    # Get previous price ID if available in previous_attributes
                    previous_items_data = previous_attributes.get('items', {}).get('data', [])
                    previous_price_id = None
                    if previous_items_data:
                         previous_price_id = previous_items_data[0].get('price', {}).get('id')

                    if current_price_id != previous_price_id:
                        plan_changed = True
                        logger.info(f"Detected plan change for customer {customer_id}. New plan: {plan_name} ({new_plan_id}) based on price {current_price_id}")
                    else:
                        logger.info(f"Subscription {subscription.id} updated, but price ID {current_price_id} hasn't changed. No plan update needed.")
                        plan_changed = False
                    # -----------------------------------------------------------------
                else:
                     logger.warning(f"Subscription {subscription.id} updated with unknown price ID: {current_price_id}. Cannot map to internal plan.")
                     plan_changed = False
            else:
                logger.warning(f"Could not extract current price ID from updated subscription {subscription.id}")
                plan_changed = False
        else:
             logger.warning(f"Subscription {subscription.id} updated event received, but no items found in subscription data.")
             plan_changed = False

        # Only process if plan actually changed and we have a valid new_plan_id
        if plan_changed and new_plan_id:
            logger.info(f"Updating database for user {user.user_id} due to plan change to {plan_name}")

            # --- ADD: Fetch current credits BEFORE deactivating ---
            try:
                old_sub_query = await db.execute(
                    select(SalesSmartStorySubscription.available_credit)
                    .where(
                        SalesSmartStorySubscription.user_id == user.user_id,
                        SalesSmartStorySubscription.active_subscription == True
                    )
                    .limit(1) # Ensure we only get one result
                )
                fetched_credits = old_sub_query.scalar_one_or_none()
                if fetched_credits is not None:
                    existing_credits = fetched_credits
                logger.debug(f"Fetched existing credits for user {user.user_id}: {existing_credits}")
            except Exception as credit_fetch_ex:
                # Log error but potentially continue, defaulting existing_credits to 0
                logger.error(f"Error fetching existing credits for user {user.user_id}: {credit_fetch_ex}", exc_info=True)
                existing_credits = 0 # Default to 0 on error to avoid blocking update
            # --- END ADD ---

            # --- 1. Deactivate Old Subscription ---
            await db.execute(
                update(SalesSmartStorySubscription)
                .where(
                    SalesSmartStorySubscription.user_id == user.user_id,
                    SalesSmartStorySubscription.active_subscription == True
                )
                .values(active_subscription=False)
            )
            logger.debug(f"Deactivated old subscription for user {user.user_id}")

            # --- 2. Create and Flush Transaction Record FIRST ---
            transaction = None
            try:
                # timestamp = datetime.datetime.now(datetime.timezone.utc).strftime('%Y%m%d%H%M%S')
                # random_suffix = ''.join(random.choices('ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789', k=6))
                # order_id = f"PI-{timestamp}-{random_suffix}"

                order_id = generate_order_id("Subscription")
                sub_start_date, sub_end_date = get_billing_period_from_subscription_retrieve(subscription)

                transaction = PaymentTransaction(
                    order_id=order_id,
                    user_id=user.user_id,
                    stripe_customer_id=customer_id,
                    stripe_subscription_id=subscription.id,
                    stripe_event_id=event.id,
                    currency="USD", # Or get from subscription/invoice if needed
                    plan_id=new_plan_id,
                    plan_name=plan_name,
                    status=PaymentStatus.completed.value,
                    transaction_metadata={
                        "event_type": StripeEvent.CUSTOMER_SUBSCRIPTION_UPDATED,
                        "change_source": "stripe_portal"
                    },
                    webhook_received_at=datetime.datetime.now(datetime.timezone.utc),
                    subscription_start_date=sub_start_date,
                    subscription_end_date=sub_end_date)
                db.add(transaction)
                await db.flush([transaction]) # Flush only this object to get its ID
                logger.debug(f"Created and flushed transaction record {transaction.transaction_id} for subscription update")

            except Exception as trans_ex:
                # Catch potential IntegrityError (like duplicate order_id) or other DB errors
                logger.error(f"Failed to create/flush transaction for subscription update: {str(trans_ex)}", exc_info=True)
                # Critical failure, rollback and exit this handler
                await db.rollback()
                # Re-raise or return to prevent further processing within this event
                raise trans_ex # Or simply return if preferred

            # --- 3. Create New Subscription using the flushed transaction_id ---
            if transaction and transaction.transaction_id: # Check if transaction creation succeeded
                try:
                    subscription_data = SalesSmartStorySubscriptionCreate(
                        user_id=user.user_id,
                        plan_id=new_plan_id,
                        transaction_id=transaction.transaction_id,
                        transaction_date=datetime.datetime.now(datetime.timezone.utc).date()
                    )

                    # Import create_subscription (ensure path is correct)
                    from endpoints.routers.subscription_routers.save_subscription import create_subscription

                    # --- MODIFY: Determine correct credits to pass using PlanReference ---
                    # Fetch the deactivated plan_id to check if it was the free plan
                    deactivated_sub_plan_id = None
                    try:
                        deactivated_sub_query = await db.execute(
                            select(SalesSmartStorySubscription.plan_id)
                            .where(
                                SalesSmartStorySubscription.user_id == user.user_id,
                                SalesSmartStorySubscription.active_subscription == False # Find the one we just deactivated
                            )
                            .order_by(SalesSmartStorySubscription.updated_at.desc()) # Get the most recently deactivated
                            .limit(1)
                        )
                        deactivated_sub_plan_id = deactivated_sub_query.scalar_one_or_none()
                    except Exception as fetch_deactivated_ex:
                         logger.error(f"Could not fetch deactivated plan_id for user {user.user_id}: {fetch_deactivated_ex}")
                         # Handle error case - default to not carrying over by setting was_free_plan=True? Or re-raise?
                         # For safety, let's default to assuming it might have been free if fetch fails
                         deactivated_sub_plan_id = None # Ensure it's None if query failed

                    # Use PlanReference defined in this file to get the Free plan ID
                    # REMOVED: from endpoints.core.config import Plans
                    was_free_plan = False
                    if deactivated_sub_plan_id is not None:
                         # Compare against the ID stored in PlanReference.free
                         was_free_plan = str(deactivated_sub_plan_id) == str(PlanReference.free.id)
                    elif not existing_credits: # Fallback: if fetch failed AND old credits were 0, assume free
                         was_free_plan = True


                    # Pass None if upgrading from free, otherwise pass existing credits
                    credits_to_pass = None if was_free_plan else existing_credits
                    logger.info(f"Determined credits to pass to create_subscription: {credits_to_pass} (was_free_plan={was_free_plan}, existing_credits={existing_credits})")

                    # Pass the correctly determined credits
                    new_sub = await create_subscription(
                        subscription_data,
                        db,
                        carry_over_credits=credits_to_pass # Use None or existing_credits
                    )
                    # --- END MODIFY ---

                    logger.debug(f"Called create_subscription for user {user.user_id} with plan {new_plan_id} and carry_over_credits {credits_to_pass}")

                    # --- 4. Update User Record ---
                    # Note: The user record update is now handled inside create_subscription
                    # (Commented out block remains here as before)
                    # is_paid = plan_name.lower() != 'free'
                    # await db.execute(
                    #     update(User)
                    #     .where(User.user_id == user.user_id)
                    #     .values(
                    #         plan_id=new_plan_id,
                    #         is_paid_user=is_paid,
                    #         is_user_provisioned=True
                    #     )
                    # )
                    # logger.debug(f"Updated user record for {user.user_id} with new plan_id {new_plan_id}")

                    # --- 5. Commit will happen in main webhook handler ---
                    logger.info(f"Successfully prepared database updates for user {user.user_id}'s subscription change.")

                except Exception as sub_user_ex:
                    logger.error(f"Error creating subscription or updating user after transaction flush: {str(sub_user_ex)}", exc_info=True)
                    # Rollback necessary as transaction was already flushed
                    await db.rollback()
                    raise sub_user_ex # Or return

            else:
                 logger.error("Transaction object or ID not available after flush, cannot proceed with subscription creation.")
                 # Rollback might be needed if flush failed partially, though unlikely
                 await db.rollback()
                 # Exit or raise

        else:
            logger.info(f"No database changes needed for subscription update event {event.id} (plan_changed={plan_changed}, new_plan_id={new_plan_id})")

    # Outer except block catches errors before transaction creation or general issues
    except Exception as e:
        logger.error(f"Error processing subscription update for user {getattr(user, 'user_id', 'UNKNOWN')}: {str(e)}", exc_info=True)
        await db.rollback() # Rollback any partial changes
        # Re-raise exception to be caught by the main webhook handler's final except block
        raise e


async def handle_subscription_deleted(event, db):
    """Handle subscription cancellations."""
    subscription = event['data']['object']


    # Find the customer
    customer_id = subscription.get('customer')
    if not customer_id:
        logger.error(f"No customer found for subscription {subscription.id}")
        return

    # Find user by Stripe customer ID
    user_query = await db.execute(
        select(User).where(User.stripe_customer_id == customer_id)
    )
    user = user_query.scalar_one_or_none()

    if not user:
        logger.error(f"No user found for Stripe customer {customer_id}")
        return

    try:
        # Find and mark the subscription as inactive
        sub_query = await db.execute(
            select(SalesSmartStorySubscription)
            .filter(
                SalesSmartStorySubscription.user_id == user.user_id,
                SalesSmartStorySubscription.active_subscription == True
            )
        )
        current_sub = sub_query.scalar_one_or_none()

        if current_sub:
            # Mark subscription as inactive
            current_sub.active_subscription = False

            # Generate order ID for the cancellation
            # timestamp = datetime.datetime.now().strftime('%Y%m%d%H%M%S')
            # random_suffix = ''.join(random.choices('ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789', k=6))
            # order_id = f"PI-{timestamp}-{random_suffix}"

            order_id = generate_order_id("Subscription")

            # Create transaction record for the cancellation
            transaction = PaymentTransaction(
                order_id=order_id,
                user_id=user.user_id,
                stripe_customer_id=customer_id,
                stripe_subscription_id=subscription.id,
                stripe_event_id=event.id,
                status=PaymentStatus.cancelled.value,
                transaction_metadata={
                    "event_type": "subscription.deleted",
                    "cancellation_reason": subscription.get("cancellation_details", {}).get("reason", "Unknown")
                },
                webhook_received_at=datetime.datetime.now()
            )
            db.add(transaction)

            # Downgrade user to free plan
            from endpoints.core.config import Plans

            # Find free plan
            plan_query = await db.execute(
                select(SalesSmartStoryPlan.plan_id)
                .where(SalesSmartStoryPlan.plan_id == Plans.Free.id)
            )
            free_plan_id = plan_query.scalar_one_or_none()

            if free_plan_id:
                # Create new subscription with free plan
                subscription_data = SalesSmartStorySubscriptionCreate(
                    user_id=user.user_id,
                    plan_id=free_plan_id,
                    transaction_date=datetime.datetime.now().date()
                )

                from endpoints.routers.subscription_routers.save_subscription import create_subscription
                await create_subscription(subscription_data, db)

                # Update user to free plan
                await db.execute(
                    update(User)
                    .where(User.user_id == user.user_id)
                    .values(is_paid_user=False, plan_id=free_plan_id)
                )

            await db.commit()
            logger.info(f"Marked subscription as canceled for user {user.user_id}")

            # Optional: Send cancellation confirmation email
            # await send_subscription_canceled_email(user.user_id, user.auth_email)
        else:
            logger.warning(f"No active subscription found for user {user.user_id} to cancel")
    except Exception as e:
        logger.error(f"Error processing subscription cancellation: {str(e)}", exc_info=True)
        await db.rollback()


async def handle_payment_intent_succeeded(event, db):
    """Handle successful one-time payments."""
    payment_intent = event['data']['object']

    # Find the customer
    customer_id = payment_intent.get('customer')
    if not customer_id:
        logger.info(f"No customer associated with payment intent {payment_intent.id}")
        return

    # Check if we already have a transaction for this payment intent
    transaction_query = await db.execute(
        select(PaymentTransaction)
        .where(PaymentTransaction.stripe_payment_intent_id == payment_intent.id)
    )
    if transaction_query.scalar_one_or_none():
        logger.info(f"Payment intent {payment_intent.id} already processed")
        return

    # Find user by Stripe customer ID
    user_query = await db.execute(
        select(User).where(User.stripe_customer_id == customer_id)
    )
    user = user_query.scalar_one_or_none()

    if not user:
        logger.error(f"No user found for Stripe customer {customer_id}")
        return

    try:
        # Get metadata from payment intent
        metadata = payment_intent.get('metadata', {})
        plan_name = metadata.get('plan', 'One-time payment')

        # Find plan_id if plan specified
        plan_id = None
        if 'plan' in metadata:
            plan_query = await db.execute(
                select(SalesSmartStoryPlan.plan_id)
                .where(SalesSmartStoryPlan.plan_desc == plan_name.lower())
            )
            plan_id = plan_query.scalar_one_or_none()

        # Generate order ID
        # timestamp = datetime.datetime.now().strftime('%Y%m%d%H%M%S')
        # random_suffix = ''.join(random.choices('ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789', k=6))
        # order_id = f"PI-{timestamp}-{random_suffix}"
        order_id = generate_order_id("Subscription")
        invoice_id = payment_intent.get('invoice')
        sub_start_date = None
        sub_end_date = None
        stripe_subscription_id = None
        if invoice_id:
            try:
                stripe_invoice = stripe.Invoice.retrieve(invoice_id)
                stripe_subscription_id = stripe_invoice.get('subscription')
                sub_start_date, sub_end_date = get_billing_period_from_subscription_retrieve(stripe_subscription_id)
            except stripe.error.StripeError as e:
                logger.warning(f"Could not retrieve subscription details via invoice {invoice_id} for PaymentIntent {payment_intent.id}: {e}")
            except Exception as e:
                logger.warning(f"Unexpected error retrieving subscription details via invoice {invoice_id} for PaymentIntent {payment_intent.id}: {e}", exc_info=True)



        # Create transaction record
        transaction = PaymentTransaction(
            order_id=order_id,
            user_id=user.user_id,
            stripe_customer_id=customer_id,
            stripe_subscription_id=stripe_subscription_id, # Store if found
            stripe_payment_intent_id=payment_intent.id,
            stripe_event_id=event.id,
            amount_cents=payment_intent.amount,
            currency=payment_intent.currency.upper(),
            plan_id=plan_id,
            plan_name=plan_name,
            status=PaymentStatus.completed.value,
            transaction_metadata={
                "payment_method_types": payment_intent.payment_method_types,
                "is_one_time": True
            },
            webhook_received_at=datetime.datetime.now(datetime.timezone.utc),
            subscription_start_date=sub_start_date,
            subscription_end_date=sub_end_date
        )
        db.add(transaction)
        await db.flush([transaction])

        # If this is a one-time payment for a plan, create a subscription
        if plan_id:
            subscription_data = SalesSmartStorySubscriptionCreate(
                user_id=user.user_id,
                plan_id=plan_id,
                transaction_id=transaction.transaction_id,
                transaction_date=datetime.datetime.now().date(),
                current_period_start=sub_start_date, # Pass the fetched dates
                current_period_end=sub_end_date     # Pass the fetched dates
            )

            from endpoints.routers.subscription_routers.save_subscription import create_subscription
            await create_subscription(subscription_data, db)

        await db.commit()
        logger.info(f"Processed one-time payment for user {user.user_id}")
    except Exception as e:
        logger.error(f"Error processing payment intent: {str(e)}", exc_info=True)
        await db.rollback()


async def handle_payment_intent_failed(event, db):
    """Handle failed one-time payments."""
    payment_intent = event['data']['object']


    # Find the customer
    customer_id = payment_intent.get('customer')
    if not customer_id:
        logger.info(f"No customer associated with payment intent {payment_intent.id}")
        return

    # Find user by Stripe customer ID
    user_query = await db.execute(
        select(User).where(User.stripe_customer_id == customer_id)
    )
    user = user_query.scalar_one_or_none()

    if not user:
        logger.error(f"No user found for Stripe customer {customer_id}")
        return

    try:
        # Get error details
        error = payment_intent.get('last_payment_error', {})
        error_message = error.get('message', 'Unknown payment error')

        # Generate order ID
        # timestamp = datetime.datetime.now().strftime('%Y%m%d%H%M%S')
        # random_suffix = ''.join(random.choices('ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789', k=6))
        # order_id = f"PI-{timestamp}-{random_suffix}"
        order_id = generate_order_id("Subscription")

        # Record the failed payment
        transaction = PaymentTransaction(
            order_id=order_id,
            user_id=user.user_id,
            stripe_customer_id=customer_id,
            stripe_payment_intent_id=payment_intent.id,
            stripe_event_id=event.id,
            amount_cents=payment_intent.amount,
            currency=payment_intent.currency.upper(),
            status=PaymentStatus.failed.value,
            error_message=error_message,
            transaction_metadata={
                "payment_method_types": payment_intent.payment_method_types,
                "is_one_time": True,
                "error_code": error.get('code')
            },
            webhook_received_at=datetime.datetime.now()
        )
        db.add(transaction)

        await db.commit()
        logger.info(f"Recorded failed payment for user {user.user_id}: {error_message}")

        # Optional: Send payment failed notification
        # await send_payment_failed_email(user.user_id, user.auth_email, error_message)
    except Exception as e:
        logger.error(f"Error recording failed payment: {str(e)}", exc_info=True)
        await db.rollback()


# @payment_router.get("/addons")
# async def addons_page(
#     request: Request,
#     user_id: UUID = Query(..., description="User ID for whom to show add-ons"),
#     db: AsyncSession = Depends(DatabaseSessionManager.get_session),
# ):
#     """Display page to purchase add-on credits relevant to the user's current plan."""
#
#     logger.info(f"Add-ons page requested for user {user_id}")
#
#     # --- Get User ---
#     user_result = await db.execute(select(User).where(User.user_id == user_id))
#     user_obj: User | None = user_result.scalar_one_or_none()
#     if not user_obj:
#         logger.error(f"User not found for add-ons page: {user_id}")
#         raise HTTPException(status_code=404, detail="User not found")
#
#     # --- Determine Current Active Plan ---
#     current_plan_name = "free" # Default if no active sub
#     sub_result = await db.execute(
#         select(SalesSmartStoryPlan.plan_desc)
#         .join(SalesSmartStorySubscription, SalesSmartStorySubscription.plan_id == SalesSmartStoryPlan.plan_id)
#         .where(
#             SalesSmartStorySubscription.user_id == user_id,
#             SalesSmartStorySubscription.active_subscription == True
#         )
#         .order_by(SalesSmartStorySubscription.created_at.desc())
#     )
#     active_plan_desc = sub_result.scalar_one_or_none()
#     if active_plan_desc:
#         current_plan_name = active_plan_desc.lower()
#
#     logger.info(f"User {user_id} current plan determined as: {current_plan_name}")
#
#     # --- Fetch and Filter Add-on Packs from Stripe ---
#     relevant_add_on_packs = []
#     # Define the product IDs for *all* packs
#     all_pack_product_ids = {
#         "prod_SAjo9ajXMFQdcs", "prod_SAjoB6qgBTavMI", "prod_SAjo9HyBky2gNp", "prod_SAjnaCLfwy2OTm",
#         "prod_SAjmDl39STsIKO", "prod_SAjmeTVxvKjxPN", "prod_SAjlkC7oPhzUho", "prod_SAjktTxBqslTX8"
#     }
#     # Determine which pack type to show based on current plan
#     required_pack_prefix = ""
#     if current_plan_name == "power":
#         required_pack_prefix = "Power Pack"
#     elif current_plan_name == "growth":
#         required_pack_prefix = "Growth Pack"
#     # Add logic here if Free users should see certain packs (e.g., Power)
#     # elif current_plan_name == "free":
#     #     required_pack_prefix = "Power Pack"
#
#     if not required_pack_prefix:
#         logger.info(f"No relevant add-on packs defined for user's plan '{current_plan_name}'.")
#     else:
#         try:
#             logger.info(f"Fetching prices for add-on packs containing prefix: '{required_pack_prefix}'")
#             prices = stripe.Price.list(active=True, type='one_time', limit=100, expand=['data.product'])
#
#             for price in prices.auto_paging_iter():
#                 if not isinstance(price.product, stripe.Product) or not price.product.active:
#                     continue
#
#                 # <<< Filter by Product Name Prefix >>>
#                 if price.product.id in all_pack_product_ids and price.product.name.startswith(required_pack_prefix):
#                     logger.info(f"Found matching pack: {price.product.name} (Price ID: {price.id})")
#                     relevant_add_on_packs.append({
#                         "name": price.product.name,
#                         "price_id": price.id,
#                         "formatted_price": f"{price.unit_amount / 100:.2f} {price.currency.upper()}"
#                     })
#
#             # Optional: Sort the relevant packs
#             relevant_add_on_packs.sort(key=lambda x: (
#                 -int(x['name'].split('–')[-1].split(' ')[1]) if 'SmartStories' in x['name'] else 0
#                )
#             )
#             logger.info(f"Found {len(relevant_add_on_packs)} relevant add-on packs for user {user_id}.")
#
#         except stripe.error.StripeError as e:
#             logger.error(f"Stripe API error fetching add-on packs for user {user_id}: {str(e)}")
#             relevant_add_on_packs = [] # Ensure empty list on error
#         except Exception as e:
#              logger.error(f"Unexpected error fetching add-on packs for user {user_id}: {str(e)}", exc_info=True)
#              relevant_add_on_packs = []
#
#
#     # --- Prepare Template Context ---
#     user_name = f"{user_obj.first_name} {user_obj.last_name}".strip().title()
#     first_initial = user_obj.first_name[0].upper() if user_obj.first_name else "U"
#     user_email = user_obj.auth_email or "No Email"
#
#     return templates.TemplateResponse(
#         "addons.html", # <<< Render the new template >>>
#         {
#             "request": request,
#             "user_id": str(user_id),
#             "user_name": user_name,
#             "first_initial": first_initial,
#             "user_email": user_email,
#             "frontend_url": frontend_url,
#             "add_on_packs": relevant_add_on_packs # Pass only relevant packs
#         }
#     )
# ... existing code ...

@payment_router.post("/create-portal-session-update")
async def create_portal_session_update(
    user_id: UUID = Body(..., embed=True),
    # If using auth: auth_data: dict = Depends(AuthService().verify_access_token_dependency),
    db: AsyncSession = Depends(DatabaseSessionManager.get_session),
):
    """
    Generate a Stripe Billing Portal session.
    For active subscribers, directs to the subscription update page.
    For users without an active subscription (e.g., free users), directs to the main portal page.
    """
    # If using auth: user_id = UUID(auth_data["sub"])
    logger.info(f"Request received for portal session for user {user_id}")

    result = await db.execute(select(User).where(User.user_id == user_id))
    user_obj: User | None = result.scalar_one_or_none()

    if not user_obj:
        logger.warning(f"User {user_id} not found for portal session request.")
        # Consider 404 Not Found if user doesn't exist
        raise HTTPException(status_code=404, detail="User not found.")

    # Ensure Stripe customer exists (required for any portal session)
    try:
        stripe_customer_id = await _ensure_stripe_customer(user_obj, db)
    except HTTPException as e:
        # Propagate error if customer creation fails
        raise e

    # Try to get the active subscription ID
    active_sub_id = await get_active_subscription_id(stripe_customer_id) # Call the modified function

    try:
        # Base configuration for the portal session
        portal_session_config: Dict[str, Any] = {
            "customer": stripe_customer_id,
            "return_url": FRONTEND_HOME,
        }

        if active_sub_id:
            # If active sub exists, add flow_data to go directly to update
            logger.info(f"User {user_id} has active subscription {active_sub_id}. Creating portal session with update flow.")
            portal_session_config["flow_data"] = {
                'type': 'subscription_update',
                'subscription_update': {  'subscription': active_sub_id }
            }
        else:
            # If no active sub (free user or canceled), create generic portal session
            logger.info(f"User {user_id} has no active Stripe subscription. Creating generic portal session.")
            # No flow_data needed, user goes to portal homepage

        # Create the session using the determined configuration
        session = stripe.billing_portal.Session.create(**portal_session_config)
        logger.info(f"Stripe portal session created: {session.id} for user {user_id}")
        return {"url": session.url}

    except stripe.error.StripeError as e:
        logger.error(f"Stripe error creating portal session for user {user_id} (Customer: {stripe_customer_id}): {str(e)}")
        raise HTTPException(status_code=500, detail=f"Could not create billing portal session: {e.user_message or str(e)}")
    except Exception as e:
        logger.error(f"Unexpected error creating portal session for user {user_id}: {str(e)}")
        raise HTTPException(status_code=500, detail="An unexpected error occurred.")


async def get_active_subscription_id(customer_id: str) -> str | None: # Return None if not found
    """Get the active subscription ID for a customer, returning None if not found."""
    try:
        subscriptions = stripe.Subscription.list(
            customer=customer_id,
            status='active',
            limit=1
        )

        if not subscriptions.data:
            logger.info(f"No active Stripe subscription found for customer {customer_id}")
            return None # Return None instead of raising error

        # Return the ID if found
        return subscriptions.data[0].id
    except stripe.error.StripeError as e:
        # Log Stripe errors but return None to allow generic portal access
        logger.error(f"Stripe error fetching subscriptions for customer {customer_id}: {e}")
        return None
    except Exception as e:
        # Log unexpected errors but return None
        logger.error(f"Unexpected error fetching subscriptions for customer {customer_id}: {e}", exc_info=True)
        return None



# === NEW PROTECTED ENDPOINT ===
@payment_router.get(
    "/relevant-addons",
    summary="Get Add-on Packs for User's Plan"
)
async def get_relevant_addons_for_user(
        auth_data: dict = Depends(AuthService().verify_access_token_dependency),
        db: AsyncSession = Depends(DatabaseSessionManager.get_session),
):
    """
    Fetches relevant add-on packs for the user's current plan,
    and filters out those already purchased in the current billing cycle.
    """
    current_user_id = UUID(auth_data["sub"])
    logger.info(f"Fetching relevant addons for authenticated user: {current_user_id}")

    # --- 1. Determine Current Active Plan (from local DB) ---
    current_plan_ref: Plan = PlanReference.free  # Default to Free plan
    active_local_subscription: SalesSmartStorySubscription | None = None

    sub_query_result = await db.execute(
        select(SalesSmartStorySubscription)  # Select the whole subscription object
        .where(
            SalesSmartStorySubscription.user_id == current_user_id,
            SalesSmartStorySubscription.active_subscription == True
        )
        .order_by(SalesSmartStorySubscription.created_at.desc())
        .limit(1)
    )
    active_local_subscription = sub_query_result.scalar_one_or_none()

    if active_local_subscription and active_local_subscription.plan_id:
        # Match the plan_id from the DB with PlanReference definitions
        matched_plan = next(
            (p for p in [PlanReference.free, PlanReference.power, PlanReference.growth]
             if p.id == str(active_local_subscription.plan_id)),
            None
        )
        if matched_plan:
            current_plan_ref = matched_plan
            logger.info(f"User {current_user_id} current plan determined as: {current_plan_ref.name}")
        else:
            logger.warning(
                f"User {current_user_id} active plan ID {active_local_subscription.plan_id} from DB not found in PlanReference. Defaulting to Free plan addons.")
    else:
        logger.info(f"User {current_user_id} has no active subscription in DB. Defaulting to Free plan addons.")

    # --- 2. Fetch Addon Price Details from Stripe ---
    available_addon_details = []
    if not current_plan_ref.addon_price_ids:
        logger.info(f"No add-on price IDs defined for plan '{current_plan_ref.name}' in PlanReference.")
        # Return empty list early if no addons are defined for the plan
        return []

    logger.info(
        f"Fetching details for addon price IDs for plan '{current_plan_ref.name}': {list(current_plan_ref.addon_price_ids.values())}")
    for addon_display_name, addon_stripe_price_id in current_plan_ref.addon_price_ids.items():
        if not addon_stripe_price_id or not addon_stripe_price_id.startswith("price_"):
            logger.warning(
                f"Skipping invalid Stripe Price ID '{addon_stripe_price_id}' for addon '{addon_display_name}' in plan '{current_plan_ref.name}'.")
            continue
        try:
            price_obj = stripe.Price.retrieve(addon_stripe_price_id)
            if price_obj.active:
                available_addon_details.append({
                    "name": addon_display_name,
                    "price_id": price_obj.id,
                    "formatted_price": f"{price_obj.unit_amount / 100:.2f} {price_obj.currency.upper()}"
                })
            else:
                logger.warning(
                    f"Skipping inactive Stripe Price ID '{addon_stripe_price_id}' for addon '{addon_display_name}'.")
        except stripe.error.StripeError as e:
            logger.error(
                f"Stripe API error retrieving price '{addon_stripe_price_id}' for addon '{addon_display_name}': {e}")
            # Optionally, decide if one failed Stripe call should prevent showing other addons.
            # For now, we log and continue, so other addons can still be displayed.
        except Exception as e:
            logger.error(
                f"Unexpected error retrieving price '{addon_stripe_price_id}' for addon '{addon_display_name}': {e}",
                exc_info=True)

    if not available_addon_details:
        logger.info(f"No active and valid addons found on Stripe for plan '{current_plan_ref.name}'.")
        return []

    # --- 3. Determine Current Billing Cycle and Filter Purchased Addons ---
    # We need the user's Stripe Customer ID to fetch their active subscription from Stripe
    user_obj = await db.scalar(select(User).where(User.user_id == current_user_id))
    if not user_obj or not user_obj.stripe_customer_id:
        logger.warning(
            f"User {current_user_id} has no Stripe customer ID. Cannot determine billing cycle for addon filtering.")
        return available_addon_details  # Return all available addons if we can't check cycle

    final_display_addons = []
    try:
        stripe_subscriptions = stripe.Subscription.list(customer=user_obj.stripe_customer_id, status='active', limit=1)
        if stripe_subscriptions.data:
            # stripe_subscriptions.data[0].get("items").data[0].get("current_period_start")
            active_stripe_sub = stripe_subscriptions.data[0].get("items").data[0]
            current_period_start_ts = active_stripe_sub.get("current_period_start")

            if current_period_start_ts:
                billing_cycle_start_date = datetime.datetime.fromtimestamp(current_period_start_ts,
                                                                           tz=datetime.timezone.utc)
                logger.info(
                    f"Current billing cycle for user {current_user_id} started at {billing_cycle_start_date.isoformat()}.")

                # Fetch completed addon transactions for this user in the current cycle
                completed_addon_txns_stmt = (
                    select(PaymentTransaction.transaction_metadata)  # Select only metadata needed
                    .where(
                        PaymentTransaction.user_id == current_user_id,
                        PaymentTransaction.status == PaymentStatus.completed.value,
                        PaymentTransaction.transaction_metadata['purchase_type'].astext == 'addon',
                        PaymentTransaction.webhook_received_at >= billing_cycle_start_date
                    )
                )
                completed_addon_txns_results = await db.execute(completed_addon_txns_stmt)
                purchased_addon_price_ids_this_cycle = {
                    txn_meta.get('addon_price_id') for (txn_meta,) in completed_addon_txns_results
                    if txn_meta and txn_meta.get('addon_price_id')
                }
                logger.info(
                    f"User {current_user_id} purchased addon price IDs this cycle: {purchased_addon_price_ids_this_cycle}")

                # Filter the available_addon_details
                for addon in available_addon_details:
                    if addon['price_id'] not in purchased_addon_price_ids_this_cycle:
                        final_display_addons.append(addon)
                    else:
                        logger.info(
                            f"Addon '{addon['name']}' (Price ID: {addon['price_id']}) already purchased by user {current_user_id} this cycle. Filtering out.")
            else:
                logger.warning(
                    f"Active Stripe subscription {active_stripe_sub.id} for user {current_user_id} has no current_period_start. Cannot filter purchased addons.")
                final_display_addons = available_addon_details  # Cannot filter, return all
        else:
            logger.info(f"User {current_user_id} has no active Stripe subscription. Cannot filter purchased addons.")
            final_display_addons = available_addon_details  # No active sub, return all available per plan

    except stripe.error.StripeError as e:
        logger.error(f"Stripe error fetching subscription for user {current_user_id} to filter addons: {e}")
        final_display_addons = available_addon_details  # On error, don't filter
    except Exception as e:
        logger.error(f"Unexpected error fetching Stripe subscription for user {current_user_id} to filter addons: {e}",
                     exc_info=True)
        final_display_addons = available_addon_details  # On error, don't filter

    logger.info(f"Returning {len(final_display_addons)} filtered addon packs for user {current_user_id}")
    print("final display addons: ", final_display_addons)
    return final_display_addons
# async def get_relevant_addons_for_user(
#     # This will use the dependency override properly
#     auth_data: dict = Depends(AuthService().verify_access_token_dependency),
#     db: AsyncSession = Depends(DatabaseSessionManager.get_session),
# ):
#     """Fetches relevant add-on packs using the user ID from the authenticated token."""
#     print('Audit: ' , auth_data)
#     current_user_id = UUID(auth_data["sub"])
#
#     logger.info(f"Fetching relevant addons for authenticated user: {current_user_id}")
#
#     # --- Determine Current Active Plan (using current_user_id) ---
#     # ... (rest of the logic is the same, uses current_user_id) ...
#     current_plan: Plan = PlanReference.free
#     # ... (Database query and PlanReference lookup) ...
#     sub_query = await db.execute(select(SalesSmartStorySubscription)
#                                  .where(SalesSmartStorySubscription.user_id == current_user_id,
#                                         SalesSmartStorySubscription.active_subscription == True)
#                                  .order_by(SalesSmartStorySubscription.created_at.desc()).limit(1))
#
#     active_plan_id_result = sub_query.scalar_one_or_none().plan_id
#     stripe_subscription_id = None
#     if active_plan_id_result:
#         plan_from_db = next((p for p in [PlanReference.free, PlanReference.power, PlanReference.growth] if p.id == str(active_plan_id_result)), None)
#         if plan_from_db:
#             current_plan = plan_from_db
#             transaction_id = sub_query.scalar_one_or_none().transaction_id
#             stripe_subscription_id = await db.execute(select(PaymentTransaction.stripe_subscription_id).where(transaction_id= sub_query.scalar_one_or_none()))
#             stripe_subscription_id = stripe_subscription_id.scalar_one_or_none()
#
#         else:
#             logger.warning(f"User {current_user_id} plan ID {active_plan_id_result} not in PlanReference. Defaulting Free.")
#     else:
#         logger.info(f"User {current_user_id} has no active subscription. Defaulting Free.")
#
#
#     # --- Fetch Price Details from Stripe ---
#     # ... (rest of the logic is the same) ...
#     addon_details = []
#     # ... (loop through current_plan.addon_price_ids, call stripe.Price.retrieve, append to addon_details) ...
#     if not current_plan.addon_price_ids:
#          logger.warning(f"No add-on price IDs defined for plan '{current_plan.name}' in PlanReference.")
#          return []
#     # ... (rest of addon fetching logic) ...
#     logger.info(f"Fetching details for addon price IDs: {list(current_plan.addon_price_ids.values())}")
#     for pack_name, price_id in current_plan.addon_price_ids.items():
#         if not price_id or not price_id.startswith("price_"):
#              logger.warning(f"Skipping invalid or placeholder Price ID '{price_id}' defined for pack '{pack_name}' in plan '{current_plan.name}' in plans.py.")
#              continue
#         try:
#             # Retrieve price details from Stripe for formatting
#             price_obj = stripe.Price.retrieve(price_id)
#             if price_obj.active:
#                  addon_details.append({
#                      "name": pack_name,
#                      "price_id": price_obj.id,
#                      "formatted_price": f"{price_obj.unit_amount / 100:.2f} {price_obj.currency.upper()}"
#                  })
#             else:
#                 logger.warning(f"Skipping inactive Stripe Price ID '{price_id}' for pack '{pack_name}'.")
#         except stripe.error.InvalidRequestError as e: logger.error(f"Stripe error: Price ID '{price_id}' not found or invalid: {e}")
#         except stripe.error.StripeError as e:
#             logger.error(f"Stripe API error retrieving price '{price_id}': {e}")
#             raise HTTPException(status_code=503, detail="Could not fetch add-on details from payment provider.")
#         except Exception as e:
#              logger.error(f"Unexpected error retrieving price '{price_id}': {e}", exc_info=True)
#              raise HTTPException(status_code=500, detail="Server error fetching add-on details.")
#
#     # Optional: Sort the results if needed (e.g., by price or name)
#     # addon_details.sort(key=lambda x: x['name'])
#
#     logger.info(f"Returning {len(addon_details)} relevant addon packs for user {current_user_id}")
#     addons_already_purchased_this_billing_cycle = None
#
#     if stripe_subscription_id:
#         sub = stripe.Subscription.retrieve(stripe_subscription_id)
#
#         subscription_items = sub.get('items', {})
#         items_data = subscription_items.get('data', [])
#
#         retrieved_subscription_data = items_data[0] if items_data else {}
#
#         start_ts = datetime.datetime.fromtimestamp(retrieved_subscription_data.get("current_period_start"))
#         end_ts = datetime.datetime.fromtimestamp(retrieved_subscription_data.get("current_period_end"))
#         addons_already_purchased_this_billing_cycle = db.execute(select(PaymentTransaction).where(status = PaymentStatus.completed.value))
#
#     return addon_details


# @payment_router.post("/create-checkout-session-addon")
# async def create_addon_checkout_session(
#     price_id: str = Body(...),
#     user_id: UUID = Body(...),
#     db: AsyncSession = Depends(DatabaseSessionManager.get_session),
# ):
#     """Create a Stripe Checkout Session for a one-time add-on purchase."""
#
#     logger.info(f"Received request for add-on checkout for user {user_id}, price_id {price_id}")
#
#     try:
#         # --- Get User and ensure they have a Stripe Customer ID ---
#         user_result = await db.execute(select(User).where(User.user_id == user_id))
#         user_obj: User | None = user_result.scalar_one_or_none()
#
#         if not user_obj:
#             logger.error(f"User not found for add-on purchase: {user_id}")
#             raise HTTPException(status_code=404, detail="User not found")
#
#         if user_obj.plan_id == PlanReference.free.id:
#             logger.error(f"User is on a free plan, cannot purchase add-on.")
#
#             raise HTTPException(status_code=400, detail="User is on a free plan, cannot purchase add-on")
#         logger.info(f"User {user_id} found with Stripe customer ID: {user_obj.stripe_customer_id}")
#
#
#         logger.info(f"Using backend base URL for add-on redirects: {backend_base_url}")
#
#         # --- Generate Order ID ---
#         # timestamp = datetime.datetime.now(datetime.timezone.utc).strftime('%Y%m%d%H%M%S')
#         # random_suffix = ''.join(random.choices('ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789', k=6))
#         # order_id = f"PI-ADDON-{timestamp}-{random_suffix}" # Distinguish add-on orders
#         order_id = generate_order_id("Addon")
#         logger.info(f"Generated Add-on Order ID: {order_id}")
#
#         # --- Create One-Time Checkout Session ---
#         logger.info(f"Creating add-on Stripe Checkout session for user {user_id}, price {price_id}")
#         checkout_session = stripe.checkout.Session.create(
#             customer=user_obj.stripe_customer_id,
#             line_items=[{'price': price_id, 'quantity': 1}],
#             mode='payment',
#             success_url=f"{backend_base_url}/payment/success?session_id={{CHECKOUT_SESSION_ID}}&type=addon",
#             cancel_url=f"{backend_base_url}/payment/cancel/?user_id={user_id}",
#             metadata={
#                 "user_id": str(user_id),
#                 "order_id": order_id,
#                 "purchase_type": "addon",
#                 "addon_price_id": price_id
#             }
#         )
#         logger.info(f"Add-on Stripe Checkout session created: {checkout_session.id}")
#
#         # --- Create Transaction Record ---
#         # Fetch price details to get amount (optional but good for records)
#         try:
#              price_obj = stripe.Price.retrieve(price_id)
#              amount_cents = price_obj.unit_amount
#              currency = price_obj.currency.upper()
#              product_id = price_obj.product
#              product = stripe.Product.retrieve(product_id)
#              plan_name = f"Addon: {product.name}" # Specific plan name for addon transaction
#         except Exception as price_err:
#              logger.error(f"Could not retrieve price details for {price_id}: {price_err}")
#              amount_cents = None # Indicate amount wasn't fetched
#              currency = "USD" # Assume USD
#              plan_name = "Addon Purchase"
#
#         transaction = PaymentTransaction(
#             order_id=order_id,
#             user_id=user_id,
#             stripe_session_id=checkout_session.id,
#             stripe_customer_id=user_obj.stripe_customer_id,
#             amount_cents=amount_cents,
#             currency=currency,
#             plan_id=None, # Add-ons might not map to your internal plans
#             plan_name=plan_name,
#             status=PaymentStatus.pending.value,
#             transaction_metadata=checkout_session.metadata # Store all metadata
#         )
#         db.add(transaction)
#         await db.commit()
#         logger.info(f"Pending Add-on PaymentTransaction {transaction.transaction_id} created for order {order_id}")
#
#         # Return the FULL URL
#         return {"sessionId": checkout_session.id, "url": checkout_session.url}
#         # return {"url": checkout_session.url}
#
#     except stripe.error.StripeError as e:
#         await db.rollback()
#         logger.error(f"Stripe error during add-on checkout creation for user {user_id}: {str(e)}")
#         raise HTTPException(status_code=400, detail=f"Stripe Error: {e.user_message or str(e)}")
#     except HTTPException as e:
#         raise e
#     except Exception as e:
#         await db.rollback()
#         logger.error(f"Unexpected error during add-on checkout creation for user {user_id}: {str(e)}", exc_info=True)
#         raise HTTPException(status_code=500, detail="An unexpected error occurred.")

# === EXISTING PROTECTED ENDPOINT (Ensure it exists and accepts price_id) ===
@payment_router.post("/create-checkout-session-addon", summary="Create Add-on Checkout Session")
async def create_checkout_session_addon(
    auth_data: dict = Depends(AuthService().verify_access_token_dependency),
    data: dict = Body(...),  # Expect {'price_id': '...'}
    db: AsyncSession = Depends(DatabaseSessionManager.get_session),
):
    """Create Stripe Checkout Session using user ID from auth and price_id from body."""
    print('Audit: ' , auth_data)

    price_id = data.get("price_id")
    # user_id from body is no longer needed for auth, use current_user_id from dependency

    # Validate price_id from body
    if not price_id or not price_id.startswith("price_"):
        raise HTTPException(status_code=400, detail="Missing or invalid 'price_id'")

    # Use the validated user ID from the dependency
    user_id =  UUID(auth_data["sub"])
    logger.info(f"Request received for add-on checkout for user {user_id}, specific price_id {price_id}")

    # --- Get User & Stripe Customer ID ---
    # ... (rest of the logic remains the same, using the validated user_id) ...
    result = await db.execute(select(User).where(User.user_id == user_id))
    user_obj: User | None = result.scalar_one_or_none()
    if not user_obj: raise HTTPException(status_code=404, detail="User not found")
    if not user_obj.stripe_customer_id: raise HTTPException(status_code=400, detail="User is not set up for payments.")

    try:
        # ... (Generate Order ID) ...
        # timestamp = datetime.datetime.now(datetime.timezone.utc).strftime('%Y%m%d%H%M%S')
        # random_suffix = ''.join(random.choices('ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789', k=6))
        # order_id = f"PI-ADDON-{timestamp}-{random_suffix}"
        order_id = generate_order_id("Addon")

        # ... (Create Stripe Session using price_id) ...
        logger.info(f"Creating add-on Stripe Checkout session for user {user_id}, price {price_id}")
        checkout_session = stripe.checkout.Session.create(
             customer=user_obj.stripe_customer_id,
             line_items=[{'price': price_id, 'quantity': 1}],
             mode='payment',
             success_url=f"{backend_base_url}/payment/success?session_id={{CHECKOUT_SESSION_ID}}&type=addon",
             cancel_url=FRONTEND_HOME,
             metadata={
                 "user_id": str(user_id), "order_id": order_id,
                 "purchase_type": "addon", "addon_price_id": price_id
             }
         )

        # ... (Create Transaction Record) ...
        amount_cents = None; currency = "usd"; plan_name = "Story Credits Add-on"
        try:
             price_obj = stripe.Price.retrieve(price_id)
             amount_cents = price_obj.unit_amount; currency = price_obj.currency.upper()
             product = stripe.Product.retrieve(price_obj.product); plan_name = f"Addon: {product.name}"
        except Exception as price_err: logger.warning(f"Could not retrieve price details for {price_id}: {price_err}")
        transaction = PaymentTransaction(
              order_id=order_id,
              user_id=user_id, stripe_session_id=checkout_session.id,
              stripe_customer_id=user_obj.stripe_customer_id, amount_cents=amount_cents,
              currency=currency, plan_id=None, plan_name=plan_name,
              status=PaymentStatus.pending.value,
              transaction_metadata=checkout_session.metadata
          )
        db.add(transaction); await db.commit()
        logger.info(f"Pending Add-on PaymentTransaction {transaction.transaction_id} created for order {order_id}")

        return {"url": checkout_session.url}
    # ... (Exception handling) ...
    except HTTPException as http_exc: await db.rollback(); raise http_exc
    except stripe.error.StripeError as e: await db.rollback(); logger.error(f"Stripe error: {e}"); raise HTTPException(status_code=400, detail=f"Payment system error: {e.user_message or str(e)}")
    except Exception as e: await db.rollback(); logger.error(f"Unexpected error: {e}", exc_info=True); raise HTTPException(status_code=500, detail="An unexpected server error.")


def get_items_data_from_subscription_retrieve(subscription: stripe.Subscription):
    """Extracts items data from the retrieved subscription."""
    subscription_items = subscription.get('items', {})
    items_data = subscription_items.get('data', [])
    return items_data

def get_billing_period_from_subscription_retrieve(subscription: stripe.Subscription):
    items_data = get_items_data_from_subscription_retrieve(subscription)
    retrieved_subscription_data = items_data[0] if items_data else {}
    start_ts = retrieved_subscription_data.get("current_period_start")
    end_ts = retrieved_subscription_data.get("current_period_end")
    sub_start_date = datetime.datetime.fromtimestamp(start_ts, tz=datetime.timezone.utc)
    sub_end_date = datetime.datetime.fromtimestamp(end_ts, tz=datetime.timezone.utc)
    return sub_start_date, sub_end_date

import pytest
from sqlalchemy.ext.asyncio import AsyncSession
from fastapi import HTTPException
from endpoints.routers.sign_up.save_user import save_user
from endpoints.models.schemas import UserCreate
from uuid import uuid4


import pytest_asyncio
from endpoints.util.database import DatabaseSessionManager
from endpoints.models.users import User


@pytest_asyncio.fixture(scope="function")
async def db_session():
    async with DatabaseSessionManager._engine.begin() as conn:
        # Create tables
        await conn.run_sync(User.metadata.create_all)

    async for session in DatabaseSessionManager.get_session():  # Corrected usage
        yield session

    async with DatabaseSessionManager._engine.begin() as conn:
        # Drop tables
        await conn.run_sync(User.metadata.drop_all)


@pytest.mark.asyncio
async def test_save_user(db_session):
    user_data = UserCreate(
        auth_provider="google",
        firebase_uid=uuid4(),
        auth_email="<EMAIL>",
        # pi_redirect_url="http://example.com",
        # first_name="Test",
        # last_name="User",
        name="Test User",
        company_name="Test Company",
        business_email="<EMAIL>",
        crm_user_sync_pending=False,
        active_status=True,
    )

    # Call the save_user function
    result = await save_user(user_data, db_session)

    # Check if the user was saved correctly
    assert result.auth_email == user_data.auth_email
    # assert result.first_name == user_data.first_name
    # assert result.last_name == user_data.last_name
    assert result.name == user_data.name

    # Check if the user exists in the database
    db_user = await db_session.get(User, result.user_id)
    assert db_user is not None
    assert db_user.auth_email == user_data.auth_email


@pytest.mark.asyncio
async def test_save_user_duplicate(db_session: AsyncSession):
    """
    Test that attempting to save a user with duplicate unique fields
    (e.g., auth_email) raises an HTTPException for data integrity error.
    """
    user_data = UserCreate(
        auth_provider="google",
        firebase_uid=str(uuid4()),
        auth_email="<EMAIL>",
        # pi_redirect_url="http://example.com",
        # first_name="Duplicate",
        # last_name="User",
        company_name="Duplicate Company",
        business_email="<EMAIL>",
        crm_user_sync_pending=False,
        active_status=True,
    )

    # Save the first user
    await save_user(user_data, db_session)

    # Attempt to save a second user with the same email
    with pytest.raises(HTTPException) as exc_info:
        await save_user(user_data, db_session)

    # Verify that a 400 error is raised for data integrity
    assert exc_info.value.status_code == 400
    assert "Data integrity error" in exc_info.value.detail

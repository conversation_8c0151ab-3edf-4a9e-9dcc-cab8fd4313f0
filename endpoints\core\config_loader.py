# config_loader.py (or your existing config module)

import toml
from dacite import from_dict, Config as DaciteConfig, MissingValueError, WrongTypeError
from pathlib import Path
import logging
import os
from typing import Optional

from endpoints.core.config import AppConfig

# Import the dataclasses we just defined


logger = logging.getLogger(__name__)

class ConfigLoadError(Exception):
    pass

# Global variable to hold the loaded configuration
# This will be populated by main.py at startup
_current_config: Optional[AppConfig] = None

class ConfigLoader:

    def load(self, path: Optional[str] = None) -> AppConfig:
        """Loads configuration from TOML into the MainConfig dataclass."""
        global _current_config

        if not path:
            env = os.getenv("CONFIG_ENVIRONMENT")
            project = os.getenv("CONFIG_PROJECT")

            path = os.getenv("CONFIG_PATH", f"endpoints/config/{env}/{project}.toml")

        if not Path(path).is_file():
            msg = f"Configuration file not found at: {path}"
            logger.error(msg)
            raise ConfigLoadError(msg)

        if _current_config:
            logger.warning("Configuration already loaded. Returning existing instance.")
            return _current_config
        try:
            logger.info(f"Loading configuration from: {path}")
            raw_config_dict = toml.load(path)

            # --- Use dacite to load the dictionary into dataclasses ---
            config_instance = from_dict(
                data_class=AppConfig,
                data=raw_config_dict,
                config=DaciteConfig(strict=True)
            )
            _current_config = config_instance

            return config_instance

        except (MissingValueError, WrongTypeError) as e:
            msg = f"Configuration error in {path}: {e}"
            logger.error(msg)
            raise ConfigLoadError(msg) from e
        except toml.TomlDecodeError as e:
            msg = f"Error decoding TOML file {path}: {e}"
            logger.error(msg)
            raise ConfigLoadError(msg) from e
        except Exception as e:
            msg = f"An unexpected error occurred loading configuration: {e}"
            logger.error(msg, exc_info=True)
            raise ConfigLoadError(msg) from e


    def get_frontend_url(self) -> str:
        if _current_config and _current_config.frontend_url:
            return _current_config.frontend_url.rstrip("/") + "/home"
        raise ConfigLoadError("Config not initialized or frontend_url not set")

    def get_backend_base_url(self) -> str:
        if _current_config and _current_config.backend_base_url:
            return _current_config.backend_base_url.rstrip("/")
        raise ConfigLoadError("Config not initialized or frontend_url not set")

    def get_support_email(self) -> str:
        if _current_config and _current_config.support_email:
            return _current_config.support_email
        logger.warning("Config not initialized or support_email not set; returning default.")
        return "<EMAIL>" # Fallback

    def get_stripe_signature_header_key(self) -> str:
        if _current_config and _current_config.stripe_config and _current_config.stripe_config.signature_header_key:
            return _current_config.stripe_config.signature_header_key
        logger.warning("Config not initialized or stripe_config.signature_header_key not set; returning default.")
        return "stripe-signature"

    def _get_current_config(self) -> AppConfig:
        """Ensures config is loaded and returns it. Loads with default path if not already loaded."""
        global _current_config
        if not _current_config:
            logger.info("Configuration not explicitly loaded. Attempting automatic default load.")
            self.load()

        if _current_config is None:
            # This state should ideally be prevented if load() always sets _current_config or raises.
            # Adding robust error for this unlikely scenario.
            logger.error("Config load was attempted (or should have been), but _current_config is still None.")
            raise ConfigLoadError("Configuration is not available after load attempt.")
        return _current_config
import logging
import os
import httpx

from uuid import UUID, uuid4
from typing import Optional

from fastapi import APIRouter, Depends, HTTPException, status
from pydantic import BaseModel, ValidationError
from sqlalchemy.exc import SQLAlchemyError
from sqlalchemy.ext.asyncio import AsyncSession

from endpoints.util.api_responses_util import standard_responses
from endpoints.util.secrets import SecretsClient
from endpoints.util.database import DatabaseSessionManager
from endpoints.models.schemas import CustomDataResponse, StoryGenResponse
from endpoints.models.stories import Story, RunStatus
from endpoints.util.email_verification import validate_email
from endpoints.util.get_credits import get_user_credits, deduct_user_credits


logger = logging.getLogger(__name__)

# Router initialization
generate_story_router = APIRouter(prefix="/user", tags=["User"])


WRAPPER_FUNCTION_URL = os.getenv("WRAPPER_FUNCTION_URL")
STORY_GENERATION_SA_EMAIL = os.getenv("STORY_GENERATION_SA_EMAIL")
REQUIRED_CREDITS = 1

import re
from uuid import UUID
from typing import Optional

from titlecase import titlecase
from pydantic import BaseModel, EmailStr, field_validator


# ────────────────────────────────────────────────────────────────────────────
# 1) “Smart-case” engine
# ────────────────────────────────────────────────────────────────────────────
class NameSanitizer:
    DEFAULT_ACRONYMS = {
        # countries / regions
        "usa", "uk", "eu", "us", "u.s.", "u.k.",
        # company suffixes
        "llc", "inc", "corp", "ltd", "co",
        # tech & misc
        "api", "saas", "sql", "ai", "db", "hr", "pr", "vp",
        # agencies
        "usdot", "faa", "dot",
    }

    def __init__(self, extra_acronyms: set[str] | None = None):
        acronyms = self.DEFAULT_ACRONYMS | (extra_acronyms or set())
        pattern = r"\b(?:" + r"|".join(map(re.escape, acronyms)) + r")\b"
        self._upper_regex = re.compile(pattern, flags=re.IGNORECASE)

    def normalize(self, raw: str) -> str:
        if raw is None:
            return raw
        # 1) collapse / trim whitespace
        s = re.sub(r"\s+", " ", str(raw)).strip()
        # 2) title-case (handles hyphens, apostrophes, small words, etc.)
        interim = titlecase(s)
        # 3) force acronyms to FULL UPPERCASE
        return self._upper_regex.sub(lambda m: m.group(0).upper(), interim)


SANITIZER = NameSanitizer()
# ------------------------------------------------------------------------------
# Pydantic model for incoming request
# ------------------------------------------------------------------------------
class GenerateStoryRequest(BaseModel):
    user_id: UUID
    prospect_email: EmailStr
    # Optional fields to be used if email validation fails
    prospect_first_name: Optional[str] = None
    prospect_last_name: Optional[str] = None
    prospect_company: Optional[str] = None
    prospect_linkedin: Optional[str] = None
    prospect_title: Optional[str] = None
    prospect_company_industry: Optional[str] = None
    prospect_company_url: Optional[str] = None

    # --- smart-case every name-like field ----------------------------------
    @field_validator(
        "prospect_first_name",
        "prospect_last_name",
        "prospect_company",
        "prospect_title",
        mode="before",
    )
    def _smart_case(cls, v: str):
        return SANITIZER.normalize(v) if v else v

    # --- ensure e-mail is stored lowercase ---------------------------------
    @field_validator("prospect_email", mode="before")
    def _email_lower(cls, v: str):
        return v.lower()


# In one of your FastAPI router files (e.g., where you handle user actions)
from pydantic import BaseModel, EmailStr


class EmailValidationRequest(BaseModel):
    email: EmailStr


@generate_story_router.post("/validate-email")
async def handle_email_validation(request: EmailValidationRequest):
    """
    Accepts an email from the frontend and validates it using a secure,
    backend-only service.
    """
    # This is the validation function you provided
    is_valid = validate_email(request.email)

    return {"is_valid": is_valid}


@generate_story_router.post(
    "/generate_story/",
    status_code=status.HTTP_202_ACCEPTED,
    response_model=CustomDataResponse[StoryGenResponse],
    responses=standard_responses(
        error_400_description="Insufficient credits or invalid request",
        error_404_description="Story generation failed",
        error_422_description="Invalid input provided",
        error_500_description="Failed to generate story or unexpected error",
    ),
)
async def generate_story(
    request: GenerateStoryRequest,
    db: AsyncSession = Depends(DatabaseSessionManager.get_session),
):
    """
    Generate a new story based on prospect information.

    Steps:
    1. Validate that the prospect email is a valid business email.
    2. If email validation fails, check if all required additional fields are provided.
    3. Check if user has sufficient credits.
    4. Create and store a new story record in 'in_progress' state.
    5. Deduct user credits.
    6. Trigger a cloud function to process story generation (fire-and-forget).
    7. Return run_id (story ID) and remaining credits.
    """

    # 1. Validate that the prospect email is a valid business email
    logger.info("ℹ️ℹ️ℹ️ Validating prospect email.")
    email_is_valid = validate_email(request.prospect_email)

    # 2. If email validation fails, check if all required additional fields are provided
    if not email_is_valid:
        logger.info("❌ Email validation failed. Checking for additional fields.")
        required_fields = [
            "prospect_first_name",
            "prospect_last_name",
            "prospect_company",
            "prospect_title"
        ]
        missing_fields = [field for field in required_fields
                         if getattr(request, field) is None or getattr(request, field) == ""]

        if missing_fields:
            logger.error(f"❌❌❌ Missing required fields when email validation failed: {missing_fields}")
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"Email validation failed. When providing an invalid email, "
                       f"the following fields are required: {', '.join(required_fields)}"
            )

    # 3. Check available credits
    logger.info("ℹ️ℹ️ℹ️ Checking user credits.")
    available_credits = await get_user_credits(db, request.user_id)
    if available_credits < REQUIRED_CREDITS:
        logger.error(
            "❌❌❌ Insufficient credits. Required: %s, Available: %s",
            REQUIRED_CREDITS,
            available_credits,
        )
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="You don't have enough credits. Please contact us to resolve this issue"
                   " at <EMAIL>"
        )

    # 4. Create new story record (with status 'in_progress')
    logger.info("Creating new story record.")
    run_id = uuid4()

    # Use provided fields or empty strings to avoid None values
    first_name = request.prospect_first_name or ""
    last_name = request.prospect_last_name or ""
    company = request.prospect_company or ""
    title = request.prospect_title or ""

    new_story = Story(
        run_id=run_id,
        user_id=request.user_id,
        title=first_name.title() + " " + last_name.title() if first_name or last_name else "New Story",
        prospect_first_name=first_name.title() if first_name else first_name,
        prospect_last_name=last_name.title() if last_name else last_name,
        prospect_email=request.prospect_email,
        prospect_company=company.title() if company else company,
        prospect_linkedin=request.prospect_linkedin,
        prospect_title=request.prospect_title.title() if request.prospect_title else request.prospect_title,
        prospect_company_industry=request.prospect_company_industry,
        prospect_company_url=request.prospect_company_url,
        status=RunStatus.in_progress.value,
    )

    try:
        # Add to the session but do not commit yet
        db.add(new_story)
        # 4. Deduct credits (commit pending until we ensure the cloud function is triggered)
        remaining_credits = await deduct_user_credits(
            db, request.user_id, REQUIRED_CREDITS
        )

        # Prepare auth token and headers for cloud function
        logger.info("Generating ID token for wrapper function via impersonation.")
        auth_token = None
        try:
            secrets_client_instance = SecretsClient() # Create an instance
            auth_token = secrets_client_instance.get_id_token_via_impersonation(
                target_principal_email=STORY_GENERATION_SA_EMAIL,
                target_audience=WRAPPER_FUNCTION_URL
            )
        except Exception as e: # Catching a broad exception as the method re-raises
            logger.exception("❌❌❌ Failed to generate ID token via SecretsClient:")
            await db.rollback()
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Failed to prepare authentication for story generation service",
            )

        headers = {
            "Authorization": f"Bearer {auth_token}",
            "Content-Type": "application/json",
        }
        # 5. Fire-and-forget trigger to cloud function (with short timeout)
        logger.info("Posting to the cloud function (fire-and-forget).")
        async with httpx.AsyncClient() as client:
            try:
                response = await client.post(
                    WRAPPER_FUNCTION_URL,
                    json=new_story.make_request(),
                    headers=headers,
                    timeout=1.0,
                )
            except httpx.TimeoutException:
                # Intentionally ignore timeout — fire-and-forget scenario
                logger.warning("⚠️⚠️⚠️ Cloud function request timed out (ignored).")
                response = None

        # If we got a response, handle non-202 or 404

        if response:
            logger.info(
                "Got response code %s when triggering cloud function",
                response.status_code,
            )
            if response.status_code == 404:
                logger.error(
                    "❌❌❌ Cloud function not found at URL: %s", WRAPPER_FUNCTION_URL
                )
                await db.rollback()
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND,
                    detail="Story generation service is currently unavailable",
                )
            elif response.status_code not in (202, 200):

                logger.error(
                    "❌❌❌ Cloud function error (Status: %s). Details: %s",
                    response.status_code,
                    response.text,
                )
                logger.error(
                    f"Handling response from cloud function. {response.json()}"
                )
                await db.rollback()

                raise HTTPException(
                    status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                    detail="Failed to initiate story generation"
                )

        # 6. Commit the new story after successfully triggering the function
        logger.info("✅✅✅ Committing the new story to the database.")
        await db.commit()

        logger.info(f"✅✅✅ Story generation initiated successfully")
        return CustomDataResponse(
            status_code=status.HTTP_202_ACCEPTED,
            detail="Story generation initiated successfully",
            data=StoryGenResponse(run_id=run_id, credits_left=remaining_credits),
        )

    except SQLAlchemyError as e:
        logger.exception(f"❌❌❌ Database error: {e}")
        await db.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Database operation failed",
        )

    except ValidationError as e:
        logger.exception("❌❌❌ Validation error:")
        await db.rollback()
        raise HTTPException(
            status_code=status.HTTP_422_UNPROCESSABLE_ENTITY,
            detail="Data validation failed",
        )

    except httpx.RequestError as e:
        logger.exception("❌❌❌ Failed to connect to cloud function:")
        await db.rollback()
        raise HTTPException(
            status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
            detail="Story generation service is currently unavailable",
        )

    except HTTPException as e:
        # Re-raise any HTTPException we explicitly threw
        logger.exception("❌❌❌HTTPException encountered:")
        await db.rollback()
        raise

    except Exception as e:
        logger.exception("❌❌❌ Unexpected error during story generation:")
        await db.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to generate story",
        )

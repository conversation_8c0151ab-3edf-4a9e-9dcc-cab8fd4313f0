from sqlalchemy import (
    Column,
    String,
    Boolean,
    TIMESTAMP,
    DateTime,
    Numeric,
    Text,
    ForeignKey,
    CheckConstraint,
    Index,
    Enum,
    func
)
from sqlalchemy.dialects.postgresql import UUID, JSONB
import uuid
import enum

from endpoints.models.base import Base


class PaymentStatus(enum.Enum):
    pending = "pending"
    processing = "processing"
    completed = "completed"
    failed = "failed"
    cancelled = "cancelled"
    refunded = "refunded"


class PaymentTransaction(Base):
    __tablename__ = "payment_transactions"

    # Primary key
    transaction_id = Column(
        UUID(as_uuid=True),
        primary_key=True,
        default=uuid.uuid4,
        nullable=False
    )

    # Order ID: PI‑{YYYYMMDDHHMMSS}-{6 alphanumeric}
    order_id = Column(
        String(50),
        nullable=False,
        unique=True,
        index=True
    )

    # User FK
    user_id = Column(
        UUID(as_uuid=True),
        ForeignKey("users.user_id", ondelete="CASCADE"),
        nullable=False,
        index=True
    )

    # Stripe references (all unique so webhook replays fail fast)
    stripe_session_id = Column(String(255), unique=True, index=True)
    stripe_customer_id = Column(String(255), unique=True)
    stripe_subscription_id = Column(String(255), unique=True)
    stripe_invoice_id = Column(String(255), unique=True)
    stripe_payment_intent_id = Column(String(255), unique=True, index=True)
    stripe_event_id = Column(String(255), unique=True, index=True)  # for idempotency

    # Amount & currency (integer cents is safest — use Numeric if you must)
    amount_cents = Column(Numeric(12, 0))
    currency = Column(String(3), nullable=False, server_default="USD")

    # Plan linkage
    plan_id = Column(
        UUID(as_uuid=True),
        ForeignKey("sales_smartstory_plans.plan_id", ondelete="SET NULL")
    )
    plan_name = Column(String(100))

    # Status
    status = Column(
        Enum(PaymentStatus, native_enum=True, name="payment_status_enum"),
        nullable=False,
        server_default=PaymentStatus.pending.value
    )

    # Free-form JSON metadata
    transaction_metadata = Column(JSONB, default=dict)

    # Error text
    error_message = Column(Text)

    # Timestamps
    created_at = Column(
        TIMESTAMP(timezone=True),
        nullable=False,
        server_default=func.now()
    )
    updated_at = Column(
        TIMESTAMP(timezone=True),
        nullable=False,
        server_default=func.now(),
        onupdate=func.now()
    )

    # Subscription window
    subscription_start_date = Column(DateTime(timezone=True))
    subscription_end_date = Column(DateTime(timezone=True))

    # Checkout & notifications
    checkout_url = Column(String(512))
    webhook_received_at = Column(TIMESTAMP(timezone=True))
    receipt_email_sent = Column(Boolean, default=False, nullable=False)

    # Table constraints, indexes, etc.
    __table_args__ = (
        CheckConstraint(
            "(order_id ~ '^PI-[0-9]{14}-[A-Z0-9]{6}$') OR (order_id ~ '^PI-ADDON-[0-9]{14}-[A-Z0-9]{6}$')",
            name="ck_payment_order_format",
        ),
        CheckConstraint(
            "currency ~ '^[A-Z]{3}$'",
            name="ck_payment_currency_format",
        ),
        Index("idx_payment_user_created", user_id, created_at.desc()),
        Index("idx_payment_status", status),
    )
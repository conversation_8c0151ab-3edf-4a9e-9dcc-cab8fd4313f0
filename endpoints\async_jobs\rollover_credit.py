from datetime import timedelta, datetime, UTC
from fastapi import Depends
from sqlalchemy.ext.asyncio import AsyncSession

from endpoints.core.config import Plans
from endpoints.models.sales_smartstory_plans import SalesSmartStoryPlan
from endpoints.models.sales_smartstory_subscriptions import SalesSmartStorySubscription

# from endpoints.models.subscription_model import SalesSmartStorySubscription
# from endpoints.models.subscripton_plan_model import SalesSmartStoryPlan
from endpoints.util.database import DatabaseSessionManager
from sqlalchemy import update, func, select
import logging

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


async def rollover_credits(
    db: AsyncSession = Depends(DatabaseSessionManager.get_session),
) -> None:
    """
    This function performs a credit rollover operation for users based on their subscription plan.
    It identifies whether a user is on a free, power, or growth plan and updates their available
    credits accordingly if they haven't been updated in the last 30 days.

    :param db: Dependency injection of the database session using `AsyncSession`.
    :type db: AsyncSession
    :return: None
    :rtype: None
    :raises Exception: Raised if any error occurs during the database update or session handling.
    """

    threshold_date = (datetime.now(UTC) - timedelta(days=30)).replace(tzinfo=None)
    # threshold_date = datetime.now(UTC) - timedelta(days=30)

    async for (
        session
    ) in DatabaseSessionManager.get_session():  # Proper async generator handling
        try:
            # Subquery to get the plan types for each subscription
            # plan_subquery = select(
            #     SalesSmartStoryPlan.plan_id, SalesSmartStoryPlan.plan_desc
            # ).subquery()

            # Update Free Plan Users: Reset available_credit to 5
            free_update_stmt = (
                update(SalesSmartStorySubscription)
                .where(
                    SalesSmartStorySubscription.plan_id == Plans.Free.id,
                    # plan_subquery.c.plan_id == '',
                    # plan_subquery.c.plan_desc.in_(["free"]),
                    SalesSmartStorySubscription.updated_at < threshold_date,
                )
                .values(available_credit=Plans.Free.credit, updated_at=func.now())
            )
            await session.execute(free_update_stmt)

            # Update Power/Growth Plan Users: Add 5 credits
            # if power and growth no longer share same credits we will need to explode this method to update them separately
            power_growth_update_stmt = (
                update(SalesSmartStorySubscription)
                .where(
                    SalesSmartStorySubscription.plan_id.in_([Plans.Power.id, Plans.Growth.id]),
                    # plan_subquery.c.plan_desc.in_(["power", "growth"]),
                    SalesSmartStorySubscription.updated_at < threshold_date,
                )
                .values(
                    available_credit=SalesSmartStorySubscription.available_credit + Plans.Power.credit,
                    updated_at=func.now(),
                )
            )
            await session.execute(power_growth_update_stmt)

            await session.commit()
            logger.info("✅✅✅ Rollover credits successfully updated in bulk.")
            break  # Exit the async generator loop after processing

        except Exception as e:
            logger.error(f"\n❌❌❌ Error during rollover credits: {e}")
            await session.rollback()
        finally:
            await session.close()  # Ensure session is closed properly

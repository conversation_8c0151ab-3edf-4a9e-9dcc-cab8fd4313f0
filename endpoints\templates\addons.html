<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="description" content="Purchase additional SmartStory credits">
    <title>Purchase Credits</title>
    <script src="https://js.stripe.com/v3/"></script>
    <script>
        // Pass user_id for JS, crucial for purchase logic
        const currentUserId = '{{user_id}}';
    </script>
    <script src="{{ url_for('static', path='script.js') }}" defer></script>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap" rel="stylesheet">
    <style>
        :root {
            /* Core colors - Copied from index.html */
            --color-primary: #4361ee;
            --color-secondary: #3a0ca3;
            --color-power: #4895ef;
            --color-growth: #3a0ca3;
            --color-enterprise: #560bad;
            --color-light: #f8f9fa;
            --color-dark: #212529;

            /* UI elements - Copied from index.html */
            --border-radius-sm: 6px;
            --border-radius: 8px;
            --border-radius-lg: 12px;
            --border-radius-xl: 20px;
            --spacing: 1rem;

            /* Shadows - Copied from index.html */
            --shadow-sm: 0 2px 4px rgba(0, 0, 0, 0.08);
            --shadow-md: 0 4px 8px rgba(0, 0, 0, 0.12);
            --shadow-lg: 0 8px 16px rgba(0, 0, 0, 0.15);
            --shadow-xl: 0 12px 24px rgba(0, 0, 0, 0.18);
            --shadow-inner: inset 0 2px 4px rgba(0, 0, 0, 0.06);

            /* Gradients - Copied from index.html */
            --gradient-primary: linear-gradient(135deg, #4361ee, #3a0ca3);
            --gradient-power: linear-gradient(135deg, #4895ef, #4361ee);
            --gradient-growth: linear-gradient(135deg, #3a0ca3, #560bad);
            --gradient-enterprise: linear-gradient(135deg, #560bad, #7209b7);
            --gradient-light: linear-gradient(135deg, rgba(255,255,255,0.9), rgba(255,255,255,0.6));
            --gradient-card: linear-gradient(180deg, #ffffff, #f8f9fc);
            --gradient-secondary: linear-gradient(135deg, #560bad, #7209b7); /* Example gradient for secondary */


            /* Transitions - Copied from index.html */
            --transition-fast: 0.2s ease;
            --transition-medium: 0.3s ease;
            --transition-slow: 0.5s ease;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', sans-serif;
            line-height: 1.6;
            color: var(--color-dark);
            background-color: #b5cbff;
            padding: 0;
            margin: 0;
            min-height: 100vh;
            display: flex;
            justify-content: center;
            align-items: center;
        }

        .container {
            max-width: 600px;
            margin: 2rem auto;
            padding: 2rem;
        }

        .card {
            background: var(--gradient-card);
            border-radius: var(--border-radius-lg);
            overflow: hidden;
            margin-bottom: 2rem;
            position: relative;
            box-shadow:
                var(--shadow-lg),
                0 0 0 1px rgba(67, 97, 238, 0.05),
                0 20px 40px rgba(67, 97, 238, 0.08);
            transition: none;
        }
         .card:hover {
            transform: none;
            box-shadow:
                var(--shadow-lg),
                0 0 0 1px rgba(67, 97, 238, 0.05),
                0 20px 40px rgba(67, 97, 238, 0.08);
        }
         .card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: var(--gradient-primary);
            z-index: 1;
        }


        .header {
            padding: 2.5rem 2rem 2rem;
            position: relative;
            text-align: center;
            background: linear-gradient(to bottom, rgba(67, 97, 238, 0.03), rgba(255, 255, 255, 0));
            border-bottom: 1px solid rgba(67, 97, 238, 0.08);
        }

        .header h1 {
            font-size: 1.75rem;
            font-weight: 600;
            color: var(--color-dark);
            margin-bottom: 0.5rem;
            letter-spacing: -0.02em;
            background: var(--gradient-primary);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .header p {
            color: #6c757d;
            font-size: 1rem;
        }

        .content {
            padding: 2rem;
        }

        /* --- User Info Styles (Copied from index.html) --- */
        .account-info {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding-bottom: 1.5rem;
            margin-bottom: 1.5rem;
            position: relative;
        }
        .account-info::after {
             content: ""; position: absolute; bottom: 0; left: 0; right: 0; height: 1px;
             background: linear-gradient(to right, rgba(67, 97, 238, 0), rgba(67, 97, 238, 0.15), rgba(67, 97, 238, 0));
        }
        .account-identity { display: flex; align-items: center; }
        .account-avatar {
             width: 48px; height: 48px; border-radius: 50%; background: var(--gradient-primary); color: white;
             display: flex; align-items: center; justify-content: center; font-weight: 600; margin-right: 1rem;
             flex-shrink: 0; box-shadow: var(--shadow-md), 0 0 0 3px rgba(255, 255, 255, 0.6), 0 0 0 4px rgba(67, 97, 238, 0.15);
             text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
        }
        .account-details { min-width: 0; }
        .account-details h3 { font-size: 1rem; margin-bottom: 0.25rem; white-space: nowrap; overflow: hidden; text-overflow: ellipsis; font-weight: 600; color: var(--color-dark); }
        .account-id { font-size: 0.875rem; color: #6c757d; white-space: nowrap; overflow: hidden; text-overflow: ellipsis; }
        /* --- End User Info Styles --- */

        /* --- Add-on Section Styles (Copied from previous index.html attempt) --- */
        .add-on-packs-section {
            margin-top: 1rem;
            padding-top: 0;
            border-top: none;
        }
        /* Title moved to header, commenting out specific h3 style */
        /* .add-on-packs-section h3 { ... } */

        .add-on-items {
            display: flex;
            flex-direction: column;
            gap: 0.75rem;
        }
        .add-on-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            background-color: rgba(255, 255, 255, 0.6);
            padding: 0.75rem 1rem;
            border-radius: var(--border-radius-sm);
            border: 1px solid rgba(67, 97, 238, 0.15);
             box-shadow: var(--shadow-sm);
             transition: transform var(--transition-fast), box-shadow var(--transition-fast);
        }
         .add-on-item:hover {
             transform: translateY(-2px);
             box-shadow: var(--shadow-md);
         }

        .add-on-item .pack-name { font-weight: 500; flex-grow: 1; margin-right: 1rem; }
        .add-on-item .pack-price { font-weight: 600; color: var(--color-primary); margin-right: 1rem; min-width: 80px; text-align: right; }
        .add-on-item .purchase-addon-button {
             padding: 0.5rem 1rem; font-size: 0.85rem; min-width: 100px; flex-shrink: 0;
             /* Inherit base .button style or define specific one */
             background: var(--gradient-secondary);
             color: white;
             border: none;
             border-radius: var(--border-radius);
             font-weight: 600;
             cursor: pointer;
             transition: transform var(--transition-fast), box-shadow var(--transition-fast), background-position var(--transition-medium);
             text-align: center;
             box-shadow: var(--shadow-md), 0 0 0 1px rgba(86, 11, 173, 0.1), 0 10px 15px -5px rgba(86, 11, 173, 0.2);
             letter-spacing: 0.2px;
             overflow: hidden;
             background-size: 200% 200%;
             background-position: 0% 0%;
        }
         .add-on-item .purchase-addon-button:hover {
             transform: translateY(-3px);
             box-shadow: var(--shadow-lg), 0 0 0 1px rgba(86, 11, 173, 0.15), 0 15px 20px -5px rgba(86, 11, 173, 0.25);
             background-position: 100% 100%;
         }
         .purchase-addon-button.loading::after {
            content: ""; position: absolute; width: 16px; height: 16px; top: 0; left: 0; right: 0; bottom: 0; margin: auto;
            border: 2px solid rgba(255, 255, 255, 0.3); border-top-color: white; border-radius: 50%;
            animation: button-loading-spinner 1s linear infinite;
        }
         @keyframes button-loading-spinner { from { transform: rotate(0turn); } to { transform: rotate(1turn); } }
        /* --- End Add-on Styles --- */

        /* Error message style (Copied from index.html) */
        .error-message {
            color: #e63946; padding: 1rem; border-radius: var(--border-radius);
            background-color: #fff5f5; margin-top: 1.5rem; display: none;
            box-shadow: var(--shadow-sm); border: 1px solid rgba(230, 57, 70, 0.2);
        }
        .error-message.visible { display: block; }

        /* Back button style (Positioned relative to body now) */
        .card-back-button {
            position: absolute;
            top: 20px;
            left: 20px;
            color: #333;
            text-decoration: none; padding: 8px; border-radius: 50%;
            background-color: rgba(255, 255, 255, 0.3);
            transition: background-color 0.2s, color 0.2s; z-index: 10;
            display: inline-flex; align-items: center; justify-content: center;
             box-shadow: var(--shadow-sm);
        }
        .card-back-button:hover {
             background-color: rgba(255, 255, 255, 0.5);
             color: #000;
        }
        .card-back-button svg { display: block; width: 24px; height: 24px; }

        /* Responsive styles (Copied from index.html) */
        @media (max-width: 768px) {
            .container { padding: 1rem; margin: 1rem auto; }
            .header, .content { padding: 1.5rem; }
            .account-info { flex-direction: column; align-items: flex-start; }
             .add-on-item { flex-direction: column; align-items: stretch; text-align: center; }
             .add-on-item .pack-name { margin-bottom: 0.5rem; margin-right: 0; }
             .add-on-item .pack-price { margin-bottom: 0.75rem; margin-right: 0; text-align: center;}
             .add-on-item .purchase-addon-button { min-width: unset; width: 100%; }
             .card-back-button { top: 10px; left: 10px; padding: 6px; }
             .card-back-button svg { width: 20px; height: 20px; }
        }

        .logo-link {
            position: absolute;
            top: 20px;
            left: 60px;
            z-index: 10;
        }

        .top-logo {
            height: 40px;
            width: auto;
            display: block;
        }

        @media (max-width: 768px) {
            .top-logo {
                height: 30px;
            }
        }

    </style>
    <meta http-equiv="Cache-Control" content="no-cache, no-store, must-revalidate">
    <meta http-equiv="Pragma" content="no-cache">
    <meta http-equiv="Expires" content="0">
</head>
<body>
    <a href="{{ frontend_url }}/home" title="Return to App Home" class="card-back-button">
        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor" width="24" height="24">
            <path d="M20 11H7.83l5.59-5.59L12 4l-8 8 8 8 1.41-1.41L7.83 13H20v-2z"/>
        </svg>
    </a>
    
    <a href="{{ frontend_url }}/home" class="logo-link">
        <img src="{{ url_for('static', path='logo_pl.png') }}" alt="Prospect Intelligence Logo" class="top-logo">
    </a>

    <div class="container">
        <div class="card">
            <header class="header">
                <h1>Purchase Additional Credits</h1>
                <p>Add more SmartStory credits to your account.</p>
            </header>

            <div class="content">
                <div class="account-info">
                    <div class="account-identity">
                         <div class="account-avatar">{{ first_initial }}</div>
                         <div class="account-details">
                             <h3>{{ user_name }}</h3>
                             <div class="account-id">{{ user_email }}</div>
                         </div>
                    </div>
                </div>

                {% if add_on_packs %}
                <div class="add-on-packs-section">
                    <div class="add-on-items">
                        {% for pack in add_on_packs %}
                        <div class="add-on-item">
                            <span class="pack-name">{{ pack.name }}</span>
                            <span class="pack-price">{{ pack.formatted_price }}</span>
                            <button class="purchase-addon-button" data-price-id="{{ pack.price_id }}">
                                Purchase
                            </button>
                        </div>
                        {% endfor %}
                    </div>
                </div>
                {% else %}
                 <div style="text-align: center; padding: 2rem 1rem; color: #555; background-color: rgba(255, 255, 255, 0.4); border-radius: var(--border-radius-sm); border: 1px solid rgba(67, 97, 238, 0.1);">
                     <p>No additional credit packs are currently available for your plan.</p>
                     <p style="margin-top: 0.5rem; font-size: 0.9em;">Consider upgrading or contact support for custom options.</p>
                 </div>
                {% endif %}

                <div id="error-message" class="error-message" role="alert"></div>

                <input type="hidden" id="user_id_field" value="{{ user_id }}">
            </div>
        </div>
    </div>
</body>
</html>

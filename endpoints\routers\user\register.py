# from typing import ClassVar
# from fastapi import APIRouter, HTTPException, Depends, status
# from pydantic import BaseModel, ConfigDict
# from pydantic import ValidationError
# from sqlalchemy.ext.asyncio import AsyncSession
# from sqlalchemy.exc import IntegrityError, SQLAlchemyError
# from sqlalchemy.future import select
# import logging
# import os
#
# from endpoints.models.users import User
# from endpoints.models.schemas import UserCreate, CustomDataResponse, RegistrationResponse
# from endpoints.util.api_responses_util import standard_responses
# from endpoints.util.database import DatabaseSessionManager
# from endpoints.auth.auth_service import AuthService
#
# user_register_router = APIRouter(prefix="/user", tags=["User"])
#
# # Configure logging
# logging.basicConfig(level=logging.INFO)
# logger = logging.getLogger(__name__)
#
#
#
#
#
# def get_auth_service():
#     # Provide a standard way for FastAPI to *construct* or *retrieve* AuthService
#     # If your AuthService requires extra config (like secret = ...), do it here.
#     return AuthService(secret=os.getenv("JWT_SECRET"))
#
#
# @user_register_router.post("/register",
#                            response_model=CustomDataResponse[RegistrationResponse],
#                            responses=standard_responses(
#                             error_400_description="Invalid data input or missing required field.",
#                             error_409_description="A user with that email already exists.",
#                             error_500_description="An unexpected error occurred while processing your request.",
#                             error_422_description="Validation Error At Server"
#                         ))
# async def register_user(
#     new_user: UserCreate,
#     db: AsyncSession = Depends(DatabaseSessionManager.get_session),
#     auth_service: AuthService = Depends(get_auth_service),
#     # security=None,
# ):
#     """
#     1. Checks if user with given auth_email already exists
#     2. If not, inserts new user
#     3. Returns a token for convenience
#     """
#     # todo uncomment before push - security
#     # if security is None:
#     #     security = []
#     try:
#         logger.info(f"Attempting to register/save user {new_user.auth_email}")
#
#         # 1) Check if user already exists
#         existing_user_query = await db.execute(
#             select(User).where(User.auth_email == new_user.auth_email)
#         )
#         existing_user = existing_user_query.scalars().first()
#         if existing_user:
#             # Return 409 conflict because the resource (the user email) already exists
#             logger.warning(f"User with email {new_user.auth_email} already exists.")
#             raise HTTPException(
#                 status_code=status.HTTP_409_CONFLICT,
#                 detail="A user with that email already exists.",
#             )
#
#         # 2) Insert the new user
#         db_user = User(
#             auth_provider=new_user.auth_provider,
#             firebase_uid=new_user.firebase_uid,
#             auth_email=new_user.auth_email,
#             # pi_redirect_url=new_user.pi_redirect_url,
#             # first_name=new_user.first_name,
#             # last_name=new_user.last_name,
#             name = new_user.name,
#             company_name=new_user.company_name,
#             business_email=new_user.business_email,
#             crm_user_sync_pending=new_user.crm_user_sync_pending,
#             active_status=new_user.active_status,
#         )
#         db.add(db_user)
#         await db.commit()
#         await db.refresh(db_user)
#
#         #    Typically "sub" for subject, but you can name it "user_id" or anything else
#         token_payload = {
#             "uid": str(db_user.user_id),
#             # "first_name": str(db_user.first_name),
#             # "last_name": str(db_user.last_name),
#             'name': str(db_user.name),
#             "provider": str(db_user.auth_provider),
#             "email": db_user.auth_email,
#         }
#         # Generate the token
#         access_token = auth_service.generate_access_token(token_payload)
#         logger.info(f"User {db_user.auth_email} registered/saved successfully")
#
#         # Return custom response with the new user info + token
#         register_user = RegistrationResponse(
#             message="User registered successfully",
#             auth_email=db_user.auth_email,
#             access_token=access_token,
#             token_type="bearer",
#         )
#         register_user = RegistrationResponse.model_validate(register_user)
#         response = CustomDataResponse(
#             status_code=status.HTTP_200_OK,
#             detail="User registered successfully",
#             data=register_user,
#         )
#         return response
#
#     except IntegrityError as e:
#         logger.error(f"Integrity error: {e}")
#         await db.rollback()
#         error_detail = str(e).lower()
#
#         # We can parse and respond with a more specific status code:
#         if "unique constraint" in error_detail:
#             # e.g. if there's some other unique field conflict
#             raise HTTPException(
#                 status_code=status.HTTP_409_CONFLICT,
#                 detail="Duplicate entry. Possibly email or token.",
#             )
#         elif "foreign key constraint" in error_detail:
#             raise HTTPException(
#                 status_code=status.HTTP_400_BAD_REQUEST,
#                 detail="Invalid reference to a related entity.",
#             )
#         elif "not-null constraint" in error_detail:
#             raise HTTPException(
#                 status_code=status.HTTP_400_BAD_REQUEST,
#                 detail="Missing required field.",
#             )
#         else:
#             raise HTTPException(
#                 status_code=status.HTTP_400_BAD_REQUEST, detail="Data validation error."
#             )
#
#     except SQLAlchemyError as e:
#         logger.error(f"Database error: {e}")
#         await db.rollback()
#         # Generic 500, but you could parse the error further if you want
#         raise HTTPException(
#             status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
#             detail=f"Database error occurred. {e}",
#         )
#     except ValidationError as e:
#         logger.error(f"Validation error: {e}")
#         raise HTTPException(
#             status_code=status.HTTP_422_UNPROCESSABLE_ENTITY,
#             detail=f"Validation Error occurred. {e}",
#         )
#     except ValueError as e:
#         logger.error(f"Unexpected error: {e}")
#         await db.rollback()
#         raise HTTPException(
#             status_code=status.HTTP_400_BAD_REQUEST,
#             detail=f"An unexpected error occurred. {e}",
#         )
#
#     except Exception as e:
#         logger.error(f"Unexpected error: {e}")
#         await db.rollback()
#         raise HTTPException(
#             status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
#             detail=f"An unexpected error occurred. {e}",
#         )

from typing import Type, Dict, Any
from fastapi.responses import JSONResponse
from pydantic import BaseModel

from endpoints.util.response_structure_model import (
    ValidationErrorResponse,
    ErrorResponse,
)


def standard_responses(
    error_208_description: str = None,
    error_400_description: str = None,
    error_401_description: str = None,
    error_403_description: str = None,
    error_404_description: str = None,
    error_409_description: str = None,
    error_422_description: str = None,
    error_429_description: str = None,
    error_500_description: str = None,
    error_503_description: str = None,
) -> Dict[int, Dict[str, Any]]:
    responses = {}

    if error_208_description:
        responses[208] = {
            "description": error_208_description,
            "model": ErrorResponse,
            "content": {
                "application/json": {"example": {"detail": error_208_description}}
            },
        }

    if error_400_description:
        responses[400] = {
            "description": error_400_description,
            "model": ErrorResponse,
            "content": {
                "application/json": {"example": {"detail": error_400_description}}
            },
        }

    if error_401_description:
        responses[401] = {
            "description": error_401_description,
            "model": ErrorResponse,
            "content": {
                "application/json": {"example": {"detail": error_401_description}}
            },
        }

    if error_403_description:
        responses[403] = {
            "description": error_403_description,
            "model": ErrorResponse,
            "content": {
                "application/json": {"example": {"detail": error_403_description}}
            },
        }

    if error_404_description:
        responses[404] = {
            "description": error_404_description,
            "model": ErrorResponse,
            "content": {
                "application/json": {"example": {"detail": error_404_description}}
            },
        }

    if error_409_description:
        responses[409] = {
            "description": error_409_description,
            "model": ErrorResponse,
            "content": {
                "application/json": {"example": {"detail": error_409_description}}
            },
        }

    if error_422_description:
        responses[422] = {
            "description": error_422_description,
            "model": ValidationErrorResponse,
            "content": {
                "application/json": {"example": {"detail": error_422_description}}
            },
        }

    if error_429_description:
        responses[429] = {
            "description": error_429_description,
            "model": ErrorResponse,
            "content": {
                "application/json": {"example": {"detail": error_429_description}}
            },
        }

    if error_500_description:
        responses[500] = {
            "description": error_500_description,
            "model": ErrorResponse,
            "content": {
                "application/json": {"example": {"detail": error_500_description}}
            },
        }

    if error_503_description:
        responses[503] = {
            "description": error_503_description,
            "model": ErrorResponse,
            "content": {
                "application/json": {"example": {"detail": error_503_description}}
            },
        }

    return responses

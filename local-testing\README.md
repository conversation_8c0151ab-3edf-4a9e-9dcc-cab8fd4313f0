# Database Diagram Documentation
## Summary

- [Introduction](#introduction)
- [Database Type](#database-type)
- [Table Structure](#table-structure)
	- [users](#users)
	- [sales_smartstory_plans](#sales_smartstory_plans)
	- [sales_smartstory_subscriptions](#sales_smartstory_subscriptions)
	- [jobs](#jobs)
	- [referrals](#referrals)
- [Relationships](#relationships)
- [Database Diagram](#database-Diagram)


## Database type

- **Database system:** PostgreSQL
## Table structure

### Users

| Name        | Type          | Settings                      | References                    | Note                           |
|-------------|---------------|-------------------------------|-------------------------------|--------------------------------|
| **user_id** | UUID | 🔑 PK, not null , default: uuid_generate_v4() |  | |
| **auth_provider** | VARCHAR(50) | not null  |  | |
| **firebase_uid** | UUID | not null , unique |  | |
| **auth_email** | VARCHAR(255) | not null , unique |  | |
| **pi_redirect_url** | VARCHAR(255) | not null  |  | |
| **first_name** | VARCHAR(100) | not null  |  | |
| **last_name** | VARCHAR(100) | not null  |  | |
| **company_name** | VARCHAR(255) | not null  |  | |
| **business_email** | VARCHAR(255) | not null , unique |  | |
| **crm_user_sync_pending** | BOOLEAN | not null , default: true |  | |
| **active_status** | BOOLEAN | not null , default: false |  | |
| **created_at** | TIMESTAMPTZ | not null , default: NOW() |  | |
| **updated_at** | TIMESTAMPTZ | not null , default: NOW() |  | |
| **paid_user** | BOOLEAN | not null  |  | | 


#### Indexes
| Name | Unique | Fields |
|------|--------|--------|
| idx_users_auth_email |  | auth_email |
### sales_smartstory_plans

| Name        | Type          | Settings                      | References                    | Note                           |
|-------------|---------------|-------------------------------|-------------------------------|--------------------------------|
| **plan_id** | UUID | 🔑 PK, not null , default: uuid_generate_v4() |  | |
| **plan_desc** | VARCHAR(50) | not null  |  | |
| **created_at** | TIMESTAMPTZ | not null , default: NOW() |  | |
| **updated_at** | TIMESTAMPTZ | not null , default: NOW() |  | |
| **active_status** | BOOLEAN | not null , default: true |  | |
| **executive_summary** | BOOLEAN | not null , default: false |  | |
| **profile_poc** | BOOLEAN | not null , default: true |  | |
| **company_overview** | BOOLEAN | not null , default: true |  | |
| **news** | BOOLEAN | not null , default: false |  | |
| **industry_trends** | BOOLEAN | not null , default: false |  | |
| **industry_challenges** | BOOLEAN | not null , default: false |  | |
| **solution_overview** | BOOLEAN | not null , default: false |  | |
| **case_studies** | BOOLEAN | not null , default: false |  | |
| **comp_intel** | BOOLEAN | not null , default: false |  | |
| **buying_guide** | BOOLEAN | not null , default: false |  | |
| **value_prop** | BOOLEAN | not null , default: false |  | |
| **bant_assessment** | BOOLEAN | not null , default: false |  | | 


### sales_smartstory_subscriptions

| Name        | Type          | Settings                      | References                    | Note                           |
|-------------|---------------|-------------------------------|-------------------------------|--------------------------------|
| **subscription_id** | UUID | 🔑 PK, not null , default: uuid_generate_v4() |  | |
| **user_id** | UUID | not null  | fk_sales_smartstory_subscriptions_user_id_users | |
| **plan_id** | UUID | not null  | fk_sales_smartstory_subscriptions_plan_id_sales_smartstory_plans | |
| **created_at** | TIMESTAMPTZ | not null , default: NOW() |  | |
| **updated_at** | TIMESTAMPTZ | not null , default: NOW() |  | |
| **active_status** | BOOLEAN | not null , default: false |  | |
| **monthly_subscribed_credit** | NUMERIC | not null , default: 0 |  | |
| **available_credit** | NUMERIC | not null , default: 0 |  | |
| **enterprise_plan_interest** | BOOLEAN | not null , default: false |  | |
| **crm_subscription_upgrade_sync_pending** | BOOLEAN | not null , default: false |  | |
| **active_subscription** | BOOLEAN | not null , default: false |  | |
| **transaction_id** | UUID | not null  |  | |
| **transaction_date** | DATE | not null  |  | |
| **override_executive_summary** | BOOLEAN | not null , default: false |  | |
| **override_profile_poc** | BOOLEAN | not null , default: false |  | |
| **override_company_overview** | BOOLEAN | not null , default: false |  | |
| **override_news** | BOOLEAN | not null , default: false |  | |
| **override_industry_trends** | BOOLEAN | not null , default: false |  | |
| **override_industry_challenges** | BOOLEAN | not null , default: false |  | |
| **override_solution_overview** | BOOLEAN | not null , default: false |  | |
| **override_case_studies** | BOOLEAN | not null , default: false |  | |
| **override_comp_intel** | BOOLEAN | not null , default: false |  | |
| **override_buying_guide** | BOOLEAN | not null , default: false |  | |
| **override_value_prop** | BOOLEAN | not null , default: false |  | |
| **override_bant_assessment** | BOOLEAN | not null , default: false |  | | 


#### Indexes
| Name | Unique | Fields |
|------|--------|--------|
| idx_sales_smartstory_subscriptions_user |  | user_id |
| idx_sales_smartstory_subscriptions_plan |  | plan_id |
### jobs

| Name        | Type          | Settings                      | References                    | Note                           |
|-------------|---------------|-------------------------------|-------------------------------|--------------------------------|
| **user_id** | UUID | 🔑 PK, not null  | fk_jobs_user_id_users | |
| **subscription_id** | UUID | 🔑 PK, not null  | fk_jobs_subscription_id_sales_smartstory_subscriptions | | 


#### Indexes
| Name | Unique | Fields |
|------|--------|--------|
| idx_jobs_user_id |  | user_id |
| idx_jobs_subscription_id |  | subscription_id |
### referrals

| Name        | Type          | Settings                      | References                    | Note                           |
|-------------|---------------|-------------------------------|-------------------------------|--------------------------------|
| **referral_id** | UUID | 🔑 PK, not null , default: uuid_generate_v4() |  | |
| **user_id** | UUID | not null  | fk_referrals_user_id_users | |
| **crm_referral_sync_pending** | BOOLEAN | not null , default: true |  | |
| **referral_email** | VARCHAR(255) | not null  |  | |
| **created_at** | TIMESTAMPTZ | not null , default: NOW() |  | | 


#### Indexes
| Name | Unique | Fields |
|------|--------|--------|
| idx_referrals_user_id |  | user_id |
## Relationships

- **sales_smartstory_subscriptions to users**: many_to_one
- **sales_smartstory_subscriptions to sales_smartstory_plans**: many_to_one
- **jobs to users**: many_to_one
- **jobs to sales_smartstory_subscriptions**: many_to_one
- **referrals to users**: many_to_one

## Database Model

![Database Diagram](Database_Design.png "DB Model")

[//]: # (```mermaid)

[//]: # (ErDiagram)

[//]: # (	sales_smartstory_subscriptions }o--|| users : references)

[//]: # (	sales_smartstory_subscriptions }o--|| sales_smartstory_plans : references)

[//]: # (	jobs }o--|| users : references)

[//]: # (	jobs }o--|| sales_smartstory_subscriptions : references)

[//]: # (	referrals }o--|| users : references)

[//]: # ()
[//]: # (	users {)

[//]: # (		UUID user_id)

[//]: # (		VARCHAR&#40;50&#41; auth_provider)

[//]: # (		UUID firebase_uid)

[//]: # (		VARCHAR&#40;255&#41; auth_email)

[//]: # (		VARCHAR&#40;255&#41; pi_redirect_url)

[//]: # (		VARCHAR&#40;100&#41; first_name)

[//]: # (		VARCHAR&#40;100&#41; last_name)

[//]: # (		VARCHAR&#40;255&#41; company_name)

[//]: # (		VARCHAR&#40;255&#41; business_email)

[//]: # (		BOOLEAN crm_user_sync_pending)

[//]: # (		BOOLEAN active_status)

[//]: # (		TIMESTAMPTZ created_at)

[//]: # (		TIMESTAMPTZ updated_at)

[//]: # (		BOOLEAN paid_user)

[//]: # (	})

[//]: # ()
[//]: # (	sales_smartstory_plans {)

[//]: # (		UUID plan_id)

[//]: # (		VARCHAR&#40;50&#41; plan_desc)

[//]: # (		TIMESTAMPTZ created_at)

[//]: # (		TIMESTAMPTZ updated_at)

[//]: # (		BOOLEAN active_status)

[//]: # (		BOOLEAN executive_summary)

[//]: # (		BOOLEAN profile_poc)

[//]: # (		BOOLEAN company_overview)

[//]: # (		BOOLEAN news)

[//]: # (		BOOLEAN industry_trends)

[//]: # (		BOOLEAN industry_challenges)

[//]: # (		BOOLEAN solution_overview)

[//]: # (		BOOLEAN case_studies)

[//]: # (		BOOLEAN comp_intel)

[//]: # (		BOOLEAN buying_guide)

[//]: # (		BOOLEAN value_prop)

[//]: # (		BOOLEAN bant_assessment)

[//]: # (	})

[//]: # ()
[//]: # (	sales_smartstory_subscriptions {)

[//]: # (		UUID subscription_id)

[//]: # (		UUID user_id)

[//]: # (		UUID plan_id)

[//]: # (		TIMESTAMPTZ created_at)

[//]: # (		TIMESTAMPTZ updated_at)

[//]: # (		BOOLEAN active_status)

[//]: # (		NUMERIC monthly_subscribed_credit)

[//]: # (		NUMERIC available_credit)

[//]: # (		BOOLEAN enterprise_plan_interest)

[//]: # (		BOOLEAN crm_subscription_upgrade_sync_pending)

[//]: # (		BOOLEAN active_subscription)

[//]: # (		UUID transaction_id)

[//]: # (		DATE transaction_date)

[//]: # (		BOOLEAN override_executive_summary)

[//]: # (		BOOLEAN override_profile_poc)

[//]: # (		BOOLEAN override_company_overview)

[//]: # (		BOOLEAN override_news)

[//]: # (		BOOLEAN override_industry_trends)

[//]: # (		BOOLEAN override_industry_challenges)

[//]: # (		BOOLEAN override_solution_overview)

[//]: # (		BOOLEAN override_case_studies)

[//]: # (		BOOLEAN override_comp_intel)

[//]: # (		BOOLEAN override_buying_guide)

[//]: # (		BOOLEAN override_value_prop)

[//]: # (		BOOLEAN override_bant_assessment)

[//]: # (	})

[//]: # ()
[//]: # (	jobs {)

[//]: # (		UUID user_id)

[//]: # (		UUID subscription_id)

[//]: # (	})

[//]: # ()
[//]: # (	referrals {)

[//]: # (		UUID referral_id)

[//]: # (		UUID user_id)

[//]: # (		BOOLEAN crm_referral_sync_pending)

[//]: # (		VARCHAR&#40;255&#41; referral_email)

[//]: # (		TIMESTAMPTZ created_at)

[//]: # (	})

[//]: # (```)
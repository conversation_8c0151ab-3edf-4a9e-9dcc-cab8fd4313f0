# ProspectIntel API Documentation

## Overview
The ProspectIntel API is a FastAPI-based backend service that provides intelligent prospect research and story generation capabilities. The API is designed to help sales professionals generate personalized stories and insights about their prospects.

**Base URL**: `https://prospect-intel-backend-[environment].us-central1.run.app`
**API Version**: Latest
**Authentication**: <PERSON>er <PERSON>ken (JWT)

## Authentication

Most endpoints require JWT authentication using Bearer tokens. Include the token in the Authorization header:
```
Authorization: Bearer <your_jwt_token>
```

### Public Endpoints (No Authentication Required)
- Root endpoint (`/`)
- User registration (`/user/save_user`)
- Payment webhooks and redirects (`/payment/*` - public routes)

### Protected Endpoints (Authentication Required)
All other endpoints require valid JWT authentication.

## API Endpoints

### 1. Root Endpoints

#### GET /
**Description**: Welcome message for the API
**Authentication**: None
**Response**: 
```json
{
  "message": "Welcome to the Prospect Intel"
}
```

---

### 2. Authentication Endpoints

#### POST /auth/login
**Description**: Authenticate user and generate access token
**Authentication**: None
**Request Body**:
```json
{
  "email": "<EMAIL>",
  "password": "password"
}
```
**Response**:
```json
{
  "status_code": 200,
  "detail": "Login successful",
  "data": {
    "access_token": "jwt_token_here",
    "token_type": "bearer",
    "expires_in": 3600
  }
}
```
**Error Responses**:
- 401: Unauthorized - Invalid credentials
- 404: User not found
- 422: Invalid input provided
- 500: Internal server error

#### POST /auth/refresh
**Description**: Refresh access token using refresh token
**Authentication**: None
**Request Body**:
```json
{
  "refresh_token": "refresh_token_here"
}
```

---

### 3. User Management Endpoints

#### POST /user/save_user
**Description**: Register new user or update existing user profile
**Authentication**: None (Public endpoint)
**Request Body**:
```json
{
  "firebase_uid": "firebase_user_id",
  "auth_email": "<EMAIL>",
  "first_name": "John",
  "last_name": "Doe",
  "company_name": "Company Inc",
  "business_email": "<EMAIL>",
  "linkedin_url": "https://linkedin.com/in/johndoe",
  "auth_provider": "firebase"
}
```
**Response**:
```json
{
  "status_code": 200,
  "detail": "User saved successfully",
  "data": {
    "user_id": "uuid",
    "token": "jwt_token",
    "is_user_provisioned": true,
    "is_user_locked_in": false,
    "plan_id": "plan_uuid"
  }
}
```

#### GET /user/{auth_email}
**Description**: Get user profile and generate authentication token
**Authentication**: None
**Parameters**:
- `auth_email`: User's authentication email
- `frontend_uid`: Frontend user ID (query parameter)
**Response**: User profile with authentication token

#### GET /user/get_homepage/{user_id}
**Description**: Get user homepage data including profile and recent stories
**Authentication**: Required
**Parameters**:
- `user_id`: UUID of the user
**Response**:
```json
{
  "status_code": 200,
  "detail": "Homepage data retrieved successfully",
  "data": {
    "user_id": "uuid",
    "auth_email": "<EMAIL>",
    "first_name": "John",
    "last_name": "Doe",
    "company_name": "Company Inc",
    "business_email": "<EMAIL>",
    "last_generated_story": {...},
    "credits_left": 10,
    "previous_story_details": [...]
  }
}
```

#### DELETE /user/
**Description**: Delete multiple users by email addresses
**Authentication**: Required
**Query Parameters**:
- `user_emails`: Comma-separated list of user emails
**Response**: Confirmation of user deletion

---

### 4. Story Generation Endpoints

#### POST /user/validate-email
**Description**: Validate if an email is a valid business email
**Authentication**: Required
**Request Body**:
```json
{
  "email": "<EMAIL>"
}
```
**Response**:
```json
{
  "is_valid": true
}
```

#### POST /user/generate_story/
**Description**: Generate a new story for a prospect
**Authentication**: Required
**Request Body**:
```json
{
  "user_id": "uuid",
  "prospect_email": "<EMAIL>",
  "prospect_first_name": "Jane",
  "prospect_last_name": "Smith",
  "prospect_company": "Target Corp",
  "prospect_linkedin": "https://linkedin.com/in/janesmith",
  "prospect_title": "VP Sales",
  "prospect_company_industry": "Technology",
  "prospect_company_url": "https://targetcorp.com"
}
```
**Response**:
```json
{
  "status_code": 202,
  "detail": "Story generation initiated",
  "data": {
    "run_id": "story_uuid",
    "credits_left": 9
  }
}
```
**Error Responses**:
- 400: Insufficient credits or invalid request
- 404: Story generation failed
- 422: Invalid input provided
- 500: Failed to generate story

#### GET /user/get_story/{run_id}
**Description**: Retrieve a generated story by run ID
**Authentication**: Required
**Parameters**:
- `run_id`: UUID of the story run
**Response**: Complete story data with prospect details and generated content

#### GET /user/prospect_list/{user_id}
**Description**: Get list of prospects for a user
**Authentication**: Required
**Parameters**:
- `user_id`: UUID of the user
**Response**: List of previous prospect stories with basic details

---

### 5. Validation Endpoints

#### GET /user/validate_user/{email}
**Description**: Validate if email is available for registration
**Authentication**: Required
**Parameters**:
- `email`: Email address to validate
**Response**:
```json
{
  "status_code": 200,
  "detail": "Email is valid",
  "data": null
}
```
**Error Responses**:
- 400: Duplicate email exists
- 422: Invalid input provided
- 500: Unexpected error

#### POST /user/add_credits/{email}
**Description**: Add credits to user account (Admin function)
**Authentication**: Required
**Parameters**:
- `email`: User's email address
**Response**: Updated credit information

---

### 6. Subscription Management Endpoints

#### GET /subscription/get_subscription/{user_id}
**Description**: Get active subscription details for a user
**Authentication**: Required
**Parameters**:
- `user_id`: UUID of the user
**Response**:
```json
{
  "status_code": 200,
  "detail": "Active subscription found",
  "data": {
    "subscription_id": "uuid",
    "user_id": "uuid",
    "plan_id": "uuid",
    "plan_name": "Power Plan",
    "transaction_id": "uuid",
    "transaction_date": "2024-01-01"
  }
}
```

#### POST /subscription/save_subscription/
**Description**: Create or update user subscription
**Authentication**: Required
**Request Body**:
```json
{
  "user_id": "uuid",
  "plan_id": "uuid",
  "transaction_id": "uuid",
  "transaction_date": "2024-01-01",
  "dollar_amt": 29.99,
  "current_period_start": "2024-01-01T00:00:00Z",
  "current_period_end": "2024-02-01T00:00:00Z"
}
```
**Response**: Created subscription details

---

### 7. Referral Endpoints

#### POST /referrals/save_referral
**Description**: Save referral emails for a user
**Authentication**: Required
**Request Body**:
```json
{
  "user_id": "uuid",
  "referral_email": [
    "<EMAIL>",
    "<EMAIL>"
  ]
}
```
**Response**:
```json
{
  "status_code": 200,
  "detail": "New Referrals Saved Successfully",
  "data": null
}
```
**Error Responses**:
- 208: All referrals were duplicates
- 400: One or more referral emails already exist
- 422: Invalid input provided
- 500: Database error

---

### 8. Payment Endpoints

#### POST /payment/create-checkout-session
**Description**: Create Stripe checkout session for subscription
**Authentication**: Required
**Request Body**: Subscription and payment details
**Response**: Stripe checkout session URL

#### POST /payment/create-checkout-session-addon
**Description**: Create checkout session for add-on purchases
**Authentication**: Required
**Request Body**:
```json
{
  "price_id": "stripe_price_id"
}
```

#### POST /payment/create-portal-session-update
**Description**: Create Stripe billing portal session
**Authentication**: Required
**Request Body**:
```json
{
  "user_id": "uuid"
}
```

#### GET /payment/get-relevant-addons
**Description**: Get available add-ons for user's current plan
**Authentication**: Required
**Response**: List of available add-on packages

#### POST /payment/webhook (Public)
**Description**: Stripe webhook endpoint for payment events
**Authentication**: None (Webhook signature verification)

#### GET /payment/success (Public)
**Description**: Payment success redirect page
**Authentication**: None

#### GET /payment/cancel (Public)
**Description**: Payment cancellation redirect page
**Authentication**: None

---

## Data Models

### User Model
```json
{
  "user_id": "uuid",
  "firebase_uid": "string",
  "auth_email": "email",
  "first_name": "string",
  "last_name": "string",
  "company_name": "string",
  "business_email": "email",
  "linkedin_url": "url",
  "auth_provider": "string",
  "is_paid_user": "boolean",
  "plan_id": "uuid",
  "is_user_provisioned": "boolean",
  "is_user_locked_in": "boolean"
}
```

### Story Model
```json
{
  "run_id": "uuid",
  "user_id": "uuid",
  "status": "string",
  "title": "string",
  "overall_summary": "object",
  "executive_summary": "object",
  "prospect": {
    "first_name": "string",
    "last_name": "string",
    "company": "string",
    "title": "string",
    "email": "email",
    "linkedin_url": "url"
  },
  "tabs": "array",
  "created_at": "datetime",
  "updated_at": "datetime"
}
```

### Subscription Model
```json
{
  "subscription_id": "uuid",
  "user_id": "uuid",
  "plan_id": "uuid",
  "plan_name": "string",
  "transaction_id": "uuid",
  "transaction_date": "date",
  "dollar_amt": "decimal",
  "current_period_start": "datetime",
  "current_period_end": "datetime",
  "active_subscription": "boolean"
}
```

## Error Handling

All endpoints return standardized error responses:

```json
{
  "status_code": 400,
  "detail": "Error description",
  "data": null
}
```

### Common HTTP Status Codes
- **200**: Success
- **202**: Accepted (for async operations)
- **208**: Already Reported (for duplicates)
- **400**: Bad Request
- **401**: Unauthorized
- **403**: Forbidden
- **404**: Not Found
- **409**: Conflict
- **422**: Unprocessable Entity (Validation Error)
- **500**: Internal Server Error

## Rate Limiting

The API implements rate limiting based on user credits and subscription plans:
- **Free Plan**: 5 credits per month
- **Power Plan**: 10 credits per month  
- **Growth Plan**: 10 credits per month

## Environment Configuration

The API supports multiple environments:
- **Development**: `prospect-intel-backend-dev-*.us-central1.run.app`
- **Production**: `prospect-intel-backend-prod-*.us-central1.run.app`

## Support

For API support and questions:
- Email: <EMAIL>
- Documentation: Available at `/docs` endpoint (Swagger UI)
- Alternative Documentation: Available at `/redoc` endpoint (ReDoc)

---

*Last Updated: 2025-01-03*
*API Version: Latest*

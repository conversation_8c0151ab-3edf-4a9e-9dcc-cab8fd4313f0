from fastapi import APIRouter, Depends, HTTPException
from pydantic import UUID4, ValidationError
from sqlalchemy import select
from sqlalchemy.exc import NoResultFound
from sqlalchemy.ext.asyncio import AsyncSession
from fastapi import status
from endpoints.models.sales_smartstory_plans import SalesSmartStoryPlan
from endpoints.models.sales_smartstory_subscriptions import SalesSmartStorySubscription
from endpoints.models.schemas import (
    CustomDataResponse,
    SalesSmartStorySubscriptionCreate,
    GetSubscriptionResponse,
)

import logging

from endpoints.util.api_responses_util import standard_responses
from endpoints.util.database import DatabaseSessionManager

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

get_subscription_router = APIRouter(prefix="/subscription", tags=["Subscriptions"])


@get_subscription_router.get(
    "/get_subscription/{user_id}",
    response_model=CustomDataResponse[GetSubscriptionResponse],
    responses=standard_responses(
        error_400_description="One or more referral emails already exist.",
        error_500_description="Database error occurred / Unexpected Error Occurred",
        error_422_description="Invalid input provided.",
    ),
)
async def get_subscription(
    user_id: UUID4,
    db: AsyncSession = Depends(DatabaseSessionManager.get_session),
):
    logger.info("ℹ️ Fetching the active subscription and plan details")
    try:
        result = await db.execute(
            select(
                SalesSmartStorySubscription.subscription_id,
                SalesSmartStorySubscription.transaction_id,
                SalesSmartStorySubscription.transaction_date,
                SalesSmartStorySubscription.user_id,
                SalesSmartStorySubscription.plan_id,
                SalesSmartStoryPlan.plan_desc.label("plan_name"),
            )
            .join(
                SalesSmartStoryPlan,
                SalesSmartStorySubscription.plan_id == SalesSmartStoryPlan.plan_id,
            )
            .filter(
                SalesSmartStorySubscription.user_id == user_id,
                SalesSmartStorySubscription.active_subscription == True,
            )
        )

        row = result.fetchone()

        if row is None:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Active subscription not found",
            )

        subscription_data = GetSubscriptionResponse(
            subscription_id=row.subscription_id,
            user_id=row.user_id,
            plan_id=row.plan_id,
            plan_name=row.plan_name,
            transaction_id=row.transaction_id,
            transaction_date=row.transaction_date,
        )

        logger.info("✅ Active subscription and plan found successfully.")
        response = CustomDataResponse(
            status_code=status.HTTP_200_OK,
            detail="Active subscription and plan found successfully",
            data=subscription_data,
        )
        return response

    except NoResultFound as nrf:
        logger.error("❌❌❌No active subscription found", exc_info=nrf)
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Active subscription not found",
        )

    except ValidationError as ve:
        logger.error(
            "❌❌❌Validation error during subscription data processing", exc_info=ve
        )
        raise HTTPException(
            status_code=status.HTTP_422_UNPROCESSABLE_ENTITY,
            detail=f"Data validation error: {ve}",
        )

    except ValueError as e:
        logger.error(f"❌❌❌ ValueError: {e}")
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST, detail=f"Invalid input: {e}"
        )

    except Exception as e:
        logger.error("❌❌❌Unexpected error fetching active subscription", exc_info=e)
        # raise
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Unexpected error fetching active subscription",
        )

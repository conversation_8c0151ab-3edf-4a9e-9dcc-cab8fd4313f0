import uuid
from sqlalchemy import (
    <PERSON>um<PERSON>,
    String,
    Boolean,
    UUID,
    TIMESTAMP,
    CheckConstraint,
    Integer,
)
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.sql import func
from endpoints.models.base import Base


class SalesSmartStoryPlan(Base):
    __tablename__ = "sales_smartstory_plans"

    plan_id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)

    plan_desc = Column(String(50), nullable=False)
    active_status = Column(Boolean, default=False)
    plan_credit = Column(Integer, nullable=False)
    executive_summary = Column(Boolean, default=False)

    profile_poc = Column(Boolean, default=False)
    prospect_personality_profile = Column(Boolean, default=False)
    hyper_messaging = Column(Boolean, default=False)
    company_overview = Column(Boolean, default=False)
    department_agency_overview = Column(Boolean, default=False)
    department_challenges = Column(Boolean, default=False)
    news = Column(Boolean, default=False)
    news_scoop = Column(Boolean, default=False)
    gov_news = Column(Boolean, default=False)
    youtube_and_videos_mentions = Column(Boolean, default=False)
    financial_summary = Column(Boolean, default=False)

    industry_trends = Column(Boolean, default=False)
    industry_challenges = Column(Boolean, default=False)
    solution_overview = Column(Boolean, default=False)
    case_studies = Column(Boolean, default=False)
    comp_intel = Column(Boolean, default=False)
    buying_guide = Column(Boolean, default=False)
    value_prop = Column(Boolean, default=False)
    bant_assessment = Column(Boolean, default=False)
    category = Column(String(60))

    created_at = Column(
        TIMESTAMP(timezone=True), server_default=func.now(), nullable=False
    )
    updated_at = Column(
        TIMESTAMP(timezone=True),
        server_default=func.now(),
        onupdate=func.now(),
        nullable=False,
    )

    __table_args__ = (
        CheckConstraint(
            "plan_desc IN ('free', 'power', 'growth')",
            name="plan_desc_check",
        ),
    )

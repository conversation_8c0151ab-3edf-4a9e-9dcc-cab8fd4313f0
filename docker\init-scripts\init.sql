-- Enable UUID extension for PostgreSQL
CREATE
EXTENSION IF NOT EXISTS "uuid-ossp";

-- Users Table
CREATE TABLE users
(
    user_id               UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    auth_provider         VARCHAR(50) CHECK (auth_provider IN ('google', 'microsoft', 'apple')),
--     pi_redirect_url VARCHAR NOT NULL,
    firebase_uid          varchar(28)  NOT NULL UNIQUE,
    auth_email            VARCHAR(255) NOT NULL UNIQUE,
    first_name            <PERSON><PERSON><PERSON><PERSON>(255),
    last_name             <PERSON><PERSON><PERSON><PERSON>(255),
    company_name          <PERSON><PERSON><PERSON><PERSON>(255),
    business_email        VARCHAR(255) UNIQUE,
    crm_user_sync_pending BOOLEAN          DEFAULT TRUE,
    active_status         BOOLEAN          DEFAULT FALSE,
    plan_id               UUID             DEFAULT NULL,
    is_paid_user          BOOLEAN          DEFAULT FALSE,
    is_user_provisioned   BOOLEAN          DEFAULT FALSE,
    is_user_locked_in     BOOLEAN          DEFAULT FALSE,
    stripe_customer_id    VARCHAR(255),
    created_at            TIMESTAMPTZ      DEFAULT NOW(),
    updated_at            TIMESTAMPTZ      DEFAULT NOW()
);


-- Referrals Table
CREATE TABLE referrals
(
    referral_id               UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id                   UUID NOT NULL,
    crm_referral_sync_pending BOOLEAN          DEFAULT TRUE,
    referral_email            VARCHAR(255),
    created_at                TIMESTAMPTZ      DEFAULT NOW(),
    CONSTRAINT fk_referrals_user FOREIGN KEY (user_id) REFERENCES users (user_id) ON DELETE CASCADE
);

-- Sales SmartStory Plans Table
CREATE TABLE sales_smartstory_plans
(
    plan_id                      UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    plan_desc                    VARCHAR(50) CHECK (plan_desc IN ('free', 'power', 'growth')),
    plan_credit                  INT not null,
    created_at                   TIMESTAMPTZ      DEFAULT NOW(),
    updated_at                   TIMESTAMPTZ      DEFAULT NOW(),
    active_status                BOOLEAN          DEFAULT FALSE,
    executive_summary            BOOLEAN          DEFAULT FALSE,
    profile_poc                  BOOLEAN          DEFAULT FALSE,
    prospect_personality_profile BOOLEAN          DEFAULT FALSE,
    hyper_messaging              BOOLEAN          DEFAULT FALSE,
    company_overview             BOOLEAN          DEFAULT FALSE,
    department_agency_overview   BOOLEAN          DEFAULT FALSE,
    department_challenges        BOOLEAN          DEFAULT FALSE,
    news                         BOOLEAN          DEFAULT FALSE,
    news_scoop                   BOOLEAN          DEFAULT FALSE,
    gov_news                     BOOLEAN          DEFAULT FALSE,
    youtube_and_videos_mentions  BOOLEAN          DEFAULT FALSE,
    industry_trends              BOOLEAN          DEFAULT FALSE,
    industry_challenges          BOOLEAN          DEFAULT FALSE,
    solution_overview            BOOLEAN          DEFAULT FALSE,
    case_studies                 BOOLEAN          DEFAULT FALSE,
    comp_intel                   BOOLEAN          DEFAULT FALSE,
    buying_guide                 BOOLEAN          DEFAULT FALSE,
    value_prop                   BOOLEAN          DEFAULT FALSE,
    bant_assessment              BOOLEAN          DEFAULT FALSE,
    category                     VARCHAR(255)     DEFAULT NULL
);

-- Sales SmartStory Subscriptions Table
CREATE TABLE sales_smartstory_subscriptions
(
    subscription_id                       UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id                               UUID NOT NULL,
    plan_id                               UUID NOT NULL,
    created_at                            TIMESTAMPTZ      DEFAULT NOW(),
    updated_at                            TIMESTAMPTZ      DEFAULT NOW(),
    active_status                         BOOLEAN          DEFAULT FALSE,
    monthly_subscribed_credit             NUMERIC          DEFAULT 0,
    available_credit                      NUMERIC          DEFAULT 0,
    enterprise_plan_interest              BOOLEAN          DEFAULT FALSE,
    crm_subscription_upgrade_sync_pending BOOLEAN          DEFAULT FALSE,
    active_subscription                   BOOLEAN          DEFAULT FALSE,
    transaction_id                        UUID,
    transaction_date                      DATE,
    dollar_amt                            NUMERIC          DEFAULT 0,
    override_executive_summary            BOOLEAN          DEFAULT TRUE,
    override_profile_poc                  BOOLEAN          DEFAULT TRUE,
    override_prospect_personality_profile BOOLEAN          DEFAULT TRUE,
    override_hyper_messaging              BOOLEAN          DEFAULT FALSE,
    override_department_agency_overview   BOOLEAN          DEFAULT TRUE,
    override_department_challenges        BOOLEAN          DEFAULT TRUE,
    override_company_overview             BOOLEAN          DEFAULT TRUE,
    override_news                         BOOLEAN          DEFAULT TRUE,
    override_news_scoop                   BOOLEAN          DEFAULT TRUE,
    override_gov_news                     BOOLEAN          DEFAULT TRUE,
    override_youtube_and_videos_mentions  BOOLEAN          DEFAULT TRUE,
    override_industry_trends              BOOLEAN          DEFAULT TRUE,
    override_industry_challenges          BOOLEAN          DEFAULT FALSE,
    override_solution_overview            BOOLEAN          DEFAULT FALSE,
    override_case_studies                 BOOLEAN          DEFAULT FALSE,
    override_comp_intel                   BOOLEAN          DEFAULT FALSE,
    override_buying_guide                 BOOLEAN          DEFAULT FALSE,
    override_value_prop                   BOOLEAN          DEFAULT FALSE,
    override_bant_assessment              BOOLEAN          DEFAULT FALSE,
    current_period_start                  TIMESTAMPTZ      DEFAULT NOW(),
    current_period_end                    TIMESTAMPTZ      DEFAULT NOW(),
    CONSTRAINT fk_subscriptions_user FOREIGN KEY (user_id) REFERENCES users (user_id) ON DELETE CASCADE,
    CONSTRAINT fk_subscriptions_plan FOREIGN KEY (plan_id) REFERENCES sales_smartstory_plans (plan_id) ON DELETE CASCADE
);

-- Jobs Table (Linking Users to Subscriptions)
CREATE TABLE jobs
(
    user_id         UUID NOT NULL,
    subscription_id UUID NOT NULL,
    PRIMARY KEY (user_id, subscription_id),
    CONSTRAINT fk_jobs_user FOREIGN KEY (user_id) REFERENCES users (user_id) ON DELETE CASCADE,
    CONSTRAINT fk_jobs_subscription FOREIGN KEY (subscription_id) REFERENCES sales_smartstory_subscriptions (subscription_id) ON DELETE CASCADE
);

CREATE TYPE run_status_enum AS ENUM ('success', 'failed', 'in_progress');
CREATE TABLE stories
(
    run_id                          UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id                         UUID         NOT NULL REFERENCES users (user_id),
    title                           VARCHAR(255) NOT NULL,

    overall_summary                 TEXT,
    executive_summary               TEXT,
    prospect_poc                    TEXT,
    prospect_personality_profile    TEXT,
    hyper_messaging                 TEXT,
    prospect_company_overview       TEXT,
    department_agency_overview      TEXT,
    industry_trends                 TEXT,
    news_scoop                      TEXT,
    gov_news                        TEXT,
    youtube_and_videos_mentions     TEXT,
    industry_challenges             TEXT,
    department_challenges           TEXT,
    solution_overview               TEXT,
    competitive_intelligence        TEXT,
    case_studies                    TEXT,
    buying_cycle                    TEXT,
    value_proposition               TEXT,
    bant_assessment                 TEXT,
    "10_k_summary"                  TEXT,
    budget_summary                  TEXT,
    linkedin_posts_summary          TEXT,
    x_formerly_twitter_post_summary TEXT,
    prospect_first_name             TEXT,
    prospect_last_name              TEXT,

    prospect_email                  VARCHAR,
    prospect_company                varchar,
    prospect_linkedin               varchar,
    prospect_title                  varchar,
    prospect_company_industry       varchar,
    prospect_company_url            VARCHAR,
    status                          run_status_enum,
    profile_picture_url               TEXT,
    overall_story_definition        JSONB,

    created_at                      TIMESTAMPTZ      DEFAULT now(),
    updated_at                      TIMESTAMPTZ      DEFAULT now()
);

create table promotions
(
    promotion_id          UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    promotion_name        VARCHAR(255) NOT NULL,
    promotion_description TEXT,
    promotion_code        VARCHAR(255) NOT NULL,
    promotion_start_date  DATE,
    promotion_end_date    DATE,
    promotion_status      BOOLEAN          DEFAULT FALSE,
    created_at            TIMESTAMPTZ      DEFAULT now(),
    updated_at            TIMESTAMPTZ      DEFAULT now()
);

CREATE TABLE promotion_redemptions
(
    id              SERIAL PRIMARY KEY,
    user_email      VARCHAR(255) NOT NULL,
    promotion_id    UUID REFERENCES promotions (promotion_id),
    promo_code_used VARCHAR(255) NOT NULL,
    registered_at   TIMESTAMPTZ DEFAULT NOW()
);

CREATE TABLE user_agreement_acceptance
(
    id                      INT GENERATED ALWAYS AS IDENTITY PRIMARY KEY,
    user_id                 UUID REFERENCES users (user_id),
    user_agreement_accepted BOOLEAN     DEFAULT FALSE,
    ip_address              VARCHAR(255),
    device_type             VARCHAR(255),
    created_at              TIMESTAMPTZ DEFAULT NOW(),
    updated_at              TIMESTAMPTZ DEFAULT NOW()
);

-- Indexing for faster lookups
-- Not implemented
CREATE INDEX idx_users_auth_email ON users (auth_email);
CREATE INDEX idx_referrals_user_id ON referrals (user_id);
CREATE INDEX idx_sales_smartstory_subscriptions_user ON sales_smartstory_subscriptions (user_id);
CREATE INDEX idx_sales_smartstory_subscriptions_plan ON sales_smartstory_subscriptions (plan_id);
CREATE INDEX idx_jobs_user_id ON jobs (user_id);
CREATE INDEX idx_jobs_subscription_id ON jobs (subscription_id);
CREATE INDEX idx_stories_user_id_created_at ON stories (user_id, created_at DESC);


-- Create an enum type for payment status
CREATE TYPE payment_status_enum AS ENUM ('pending', 'processing', 'completed', 'failed', 'cancelled', 'refunded');

-- Create the payment_transactions table
CREATE TABLE payment_transactions
(
    -- Primary key
    transaction_id           UUID PRIMARY KEY             DEFAULT uuid_generate_v4(),
    -- Order ID: PI-{YYYYMMDDHHMMSS}-{6 alphanum} OR PI-ADDON-{YYYYMMDDHHMMSS}-{6 alphanum}
    order_id                 VARCHAR(50)         NOT NULL UNIQUE,

    -- User FK
    user_id                  UUID                NOT NULL REFERENCES users (user_id) ON DELETE CASCADE,

    -- Stripe references (all unique for idempotency)
    stripe_session_id        VARCHAR(255) UNIQUE,
    stripe_customer_id       VARCHAR(255),
    stripe_subscription_id   VARCHAR(255),
    stripe_invoice_id        VARCHAR(255) UNIQUE,
    stripe_payment_intent_id VARCHAR(255),
    stripe_event_id          VARCHAR(255) UNIQUE,     -- For webhook idempotency

    -- Amount & currency (integer cents is safest for money)
    amount_cents             NUMERIC(12, 0),
    currency                 VARCHAR(3)          NOT NULL DEFAULT 'USD',

    -- Plan information
    plan_id                  UUID                REFERENCES sales_smartstory_plans (plan_id) ON DELETE SET NULL,
    plan_name                VARCHAR(100),

    -- Transaction status
    status                   payment_status_enum NOT NULL DEFAULT 'pending',

    -- Transaction metadata - RENAMED FROM "metadata"
    transaction_metadata     JSONB                        DEFAULT '{}',

    -- Error handling
    error_message            TEXT,

    -- Timestamps with proper defaults
    created_at               TIMESTAMPTZ         NOT NULL DEFAULT NOW(),
    updated_at               TIMESTAMPTZ         NOT NULL DEFAULT NOW(),

    -- Subscription period dates
    subscription_start_date  TIMESTAMPTZ,
    subscription_end_date    TIMESTAMPTZ,

    -- Checkout and notification tracking
    checkout_url             VARCHAR(512),
    webhook_received_at      TIMESTAMPTZ,
    receipt_email_sent       BOOLEAN             NOT NULL DEFAULT FALSE,

    -- Constraints for data integrity
    CONSTRAINT ck_payment_order_format CHECK (
        order_id ~ '^PI-[0-9]{14}-[A-Z0-9]{6}$'       -- Original format
        OR
        order_id ~ '^PI-ADDON-[0-9]{14}-[A-Z0-9]{6}$' -- New addon format
) ,
    CONSTRAINT ck_payment_currency_format CHECK (currency ~ '^[A-Z]{3}$')
);

-- Indexes for performance
CREATE INDEX idx_payment_user_created ON payment_transactions (user_id, created_at DESC);
CREATE INDEX idx_payment_status ON payment_transactions (status);
CREATE INDEX idx_payment_session_id ON payment_transactions (stripe_session_id);
CREATE INDEX idx_payment_payment_intent_id ON payment_transactions (stripe_payment_intent_id);
CREATE INDEX idx_payment_event_id ON payment_transactions (stripe_event_id);

-- Insert data into sales_smartstory_plans table first (no foreign key dependencies)
INSERT INTO sales_smartstory_plans (plan_id, plan_desc, active_status, plan_credit, executive_summary, profile_poc,
                                    company_overview, news, industry_trends, industry_challenges, solution_overview,
                                    case_studies, comp_intel, buying_guide, value_prop, bant_assessment)
VALUES ('f47ac10b-58cc-4372-a567-0e02b2c3d479', 'free', TRUE, 5, TRUE, TRUE, FALSE, FALSE, FALSE, FALSE, FALSE, FALSE,
        FALSE, FALSE, FALSE, FALSE),
       ('550e8400-e29b-41d4-a716-************', 'power', TRUE, 10, TRUE, TRUE, TRUE, TRUE, TRUE, FALSE, FALSE, FALSE,
        FALSE, FALSE, FALSE, FALSE),
       ('6ba7b810-9dad-11d1-80b4-00c04fd430c8', 'growth', TRUE, 10, TRUE, TRUE, TRUE, TRUE, TRUE, TRUE, TRUE, TRUE,
        FALSE, FALSE, FALSE, FALSE);


--
-- -- Add columns to 'stories' table
-- ALTER TABLE stories ADD COLUMN youtube_and_video_mentions TEXT;
-- ALTER TABLE stories ADD COLUMN gov_news TEXT;
-- ALTER TABLE stories ADD COLUMN profile_picture_url TEXT;
-- ALTER TABLE stories ADD COLUMN hyper_messaging TEXT;
--
-- -- Add boolean columns to 'sales_smartstory_plans' table
-- ALTER TABLE sales_smartstory_plans ADD COLUMN youtube_and_video_mentions BOOLEAN;
-- ALTER TABLE sales_smartstory_plans ADD COLUMN gov_news BOOLEAN;
-- ALTER TABLE sales_smartstory_plans ADD COLUMN hyper_messaging BOOLEAN;
--
-- -- Add override boolean columns to 'sales_smartstory_subscriptions' table
-- ALTER TABLE sales_smartstory_subscriptions ADD COLUMN override_youtube_and_video_mentions BOOLEAN;
-- ALTER TABLE sales_smartstory_subscriptions ADD COLUMN override_gov_news BOOLEAN;
-- ALTER TABLE sales_smartstory_subscriptions ADD COLUMN override_hyper_messaging BOOLEAN;

from sqlalchemy import Column, String, Boolean, UUID, ForeignKey
import uuid

from endpoints.models.base import Base


class Referral(Base):  # Correct ORM Model
    __tablename__ = "referrals"

    referral_id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    user_id = Column(
        UUID(as_uuid=True),
        ForeignKey("users.user_id", ondelete="CASCADE"),
        nullable=False,
    )
    referral_email = Column(String(255), nullable=True)
    crm_referral_sync_pending = Column(Boolean, default=True)
    created_at = Column(String, server_default="NOW()")  # PostgreSQL timestamp

import os
import uuid
from sqlalchemy import (
    Column,
    String,
    Text,
    ForeignKey,
    JSON,
    Boolean,
    TIMESTAMP,
    CheckConstraint,
    Enum,
)
from sqlalchemy.dialects.postgresql import UUID, JSONB
from sqlalchemy.sql import func
from endpoints.models.base import Base
import enum


class RunStatus(enum.Enum):
    success = "success"
    failed = "failed"
    in_progress = "in_progress"


class Story(Base):
    __tablename__ = "stories"

    run_id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    user_id = Column(UUID(as_uuid=True), ForeignKey("users.user_id"), nullable=False)
    title = Column(String(255), nullable=False)

    overall_summary = Column(Text)
    executive_summary = Column(Text)
    prospect_poc = Column(Text, nullable=False)
    hyper_messaging = Column(Text, nullable=True)
    prospect_company_overview = Column(Text, nullable=False)
    department_agency_overview = Column(Text, nullable=False)
    
    prospect_personality_profile = Column(Text, nullable=False)
    financial_summary = Column(Text, nullable=True)
    industry_trends = Column(Text, nullable=False)
    news_scoop = Column(Text, nullable=True)
    gov_news = Column(Text, nullable=True)
    youtube_and_videos_mentions = Column(Text, nullable=True)

    industry_challenges = Column(Text)
    department_challenges = Column(Text)
    solution_overview = Column(Text)
    competitive_intelligence = Column(Text)
    case_studies = Column(Text)
    buying_cycle = Column(Text)
    value_proposition = Column(Text)
    bant_assessment = Column(Text)
    summary_10k = Column("10_k_summary", Text)
    linkedin_posts_summary = Column(Text)
    x_formerly_twitter_post_summary = Column(Text)

    prospect_first_name = Column(Text)
    prospect_last_name = Column(Text)
    prospect_email = Column(String)
    prospect_company = Column(String)
    prospect_linkedin = Column(String)
    prospect_title = Column(String)
    prospect_company_industry = Column(String)
    prospect_company_url = Column(String)
    profile_picture_url = Column(String, nullable=True)

    status = Column(Enum(RunStatus, name="run_status_enum"), nullable=True)
    # the below overall_story_definition is completely optional as it used for the backup.
    overall_story_definition = Column(JSONB)

    created_at = Column(
        TIMESTAMP(timezone=True), server_default=func.now(), nullable=False
    )
    updated_at = Column(
        TIMESTAMP(timezone=True),
        server_default=func.now(),
        onupdate=func.now(),
        nullable=False,
    )

    def make_request(self):
        override_values = {'override_config': {'database': 'postgres_dev'}} if os.getenv('TEST_INSTANCE',
                                                                                         'false') == 'true' else {}
        return {
            "run_id": str(self.run_id),
            "title": self.title,
            "user_id": str(self.user_id),
            "lead_id": str(self.run_id),
            "prospect_name": self.prospect_first_name + " " + self.prospect_last_name,
            "prospect_company": self.prospect_company,
            "prospect_email": self.prospect_email,
            "prospect_linkedin": self.prospect_linkedin,
            "prospect_title": self.prospect_title,
            "prospect_company_industry": self.prospect_company_industry,
            "prospect_company_url": self.prospect_company_url,
            **override_values
        }

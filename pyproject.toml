[project]
name = "backend-endpoint-services"
version = "0.1.0"
description = ""
authors = [
    {name = "abdul",email = "<EMAIL>"}
]
readme = "README.md"
requires-python = ">=3.11,<4.0"
dependencies = [
    "fastapi (==0.115.8)",
    "uvicorn (==0.34.0)",
    "asyncpg (==0.30.0)",
    "sqlalchemy (==2.0.38)",
    "cloud-sql-python-connector[asyncpg] (==1.17.0)",
    "greenlet (==3.1.1)",
    "black (==25.1.0)",
    "poetry (==2.1.1)",
    "pydantic[email] (==2.10.6)",
    "pyjwt (>=2.10.1,<3.0.0)",
    "requests (>=2.32.3,<3.0.0)",
    "python-dotenv (>=1.0.1,<2.0.0)",
    "cryptography (>=44.0.1,<45.0.0)",
    "testcontainers (>=4.9.1,<5.0.0)",
    "google-cloud-logging (>=3.11.4,<4.0.0)",
    "quickemailverification (==1.0.4)",
    "pydantic-settings>=2.0.0,<3.0.0",
    "uvloop (==0.21.0)",
    "google-cloud-secret-manager (>=2.23.2,<3.0.0)",
    "stripe (>=12.0.0,<13.0.0)",
    "jinja2 (>=3.1.6,<4.0.0)",
    "alembic (>=1.15.2,<2.0.0)",
        "toml (>=0.10.2,<0.11.0)",
        "dacite (>=1.9.2,<2.0.0)",
        "titlecase (>=2.4.1,<3.0.0)",
]


[build-system]
requires = ["poetry-core>=2.0.0,<3.0.0"]
build-backend = "poetry.core.masonry.api"

[tool.poetry.group.dev.dependencies]
fastapi = "0.115.8"
uvicorn = "0.34.0"
asyncpg = "0.30.0"
sqlalchemy = "2.0.38"
cloud-sql-python-connector = {version = "1.17.0", extras = ["asyncpg"]}
greenlet = "3.1.1"
uvloop = "0.21.0"
black = "25.1.0"
poetry = "2.1.1"
pytest = "8.3.4"
pytest-asyncio = "0.25.3"
pydantic = {extras = ["email"], version = "^2.10.6"}
google-cloud-logging = "^3.11.4"

[tool.pytest.ini_options]
asyncio_mode = "strict"
asyncio_default_fixture_loop_scope = "function"

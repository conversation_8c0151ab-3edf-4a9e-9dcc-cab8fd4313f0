import logging
from fastapi import APIRouter, Depends, HTTPException, status
from pydantic import BaseModel, EmailStr, ValidationError, UUID4
from sqlalchemy.exc import SQLAlchemyError, IntegrityError
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.future import select

from endpoints.util.api_responses_util import standard_responses
from endpoints.util.database import DatabaseSessionManager
from endpoints.models.schemas import (
    CustomDataResponse,
    UserHomePageResponse,
    HomepagePreviousRuns,
)
from endpoints.models.users import User
from endpoints.models.stories import Story
from endpoints.util.get_credits import get_user_credits
from endpoints.util.story_response_builder import create_public_story_response
from endpoints.util.subscription_helper import get_active_subscription

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# -----------------------
# Router Initialization
# -----------------------
homepage_router = APIRouter(prefix="/user", tags=["User"])
# Constants

HOMEPAGE_PREVIOUS_RUNS_LIMIT = 60

@homepage_router.get(
    "/get_homepage/{user_id}",
    response_model=CustomDataResponse[UserHomePageResponse],
    responses=standard_responses(
        error_404_description="User not found",
        error_500_description="Database error occurred / Unexpected Error Occurred",
        error_422_description="Invalid input provided",
    ),
)
async def get_user_homepage(
        user_id: UUID4,
        db: AsyncSession = Depends(DatabaseSessionManager.get_session),
):
    """
    Retrieve homepage data for a user including their profile and previous story runs.

    Args:
        user_id: UUID of the user
        db: Database session

    Returns:
        CustomDataResponse containing the homepage data including user profile and previous stories
    """
    try:
        logger.info(f"Fetching homepage data for user ID: {user_id}")

        # Get user data
        user_result = await db.execute(
            select(User).filter(User.user_id == user_id)
        )
        user = user_result.scalar_one_or_none()
        logger.info(f"✅✅✅User Found: {user.first_name} {user.last_name}")
        
        if not user:
            logger.error(f"⚠️⚠️⚠️User with ID {user_id} not found")
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="User not found"
            )

        # Get previous stories
        stories_result = await db.execute(
            select(Story)
            .filter(Story.user_id == user_id)
            .order_by(Story.created_at.desc())
            .limit(HOMEPAGE_PREVIOUS_RUNS_LIMIT)
        )
        stories = stories_result.scalars().all()
        
        # If user exists but has no stories (new user)
        if not stories:
            logger.info(f"ℹ️ℹ️ℹ️New user detected - no stories found for user ID: {user_id}")
            homepage_data = UserHomePageResponse(
                user_id=user.user_id,
                auth_email=user.auth_email,
                first_name=user.first_name,
                last_name=user.last_name,
                company_name=user.company_name,
                business_email=user.business_email,
                last_generated_story=None,
                credits_left=await get_user_credits(db, user_id),
                previous_story_details=[]
            )
            return CustomDataResponse(
                status_code=status.HTTP_200_OK,
                detail="New user homepage data retrieved successfully",
                data=homepage_data
            )

        # Get last generated story if exists
        last_story = stories[0] if stories else None

        logger.info(f"✅✅✅Last generated story fetched: {last_story}")

        # Convert stories to HomepagePreviousRuns format
        previous_runs = []
        for story in stories:
            try:
                previous_run = HomepagePreviousRuns(
                    run_id=story.run_id,
                    status=story.status.value,
                    name=f"{story.prospect_first_name or ''} {story.prospect_last_name or ''}".strip() or "",
                    company_name=story.prospect_company or "",  # Provide default empty string if None
                    created_at=story.created_at
                )
                previous_runs.append(previous_run)
            except Exception as e:
                logger.error(f"Error converting story to HomepagePreviousRuns: {e}")
                continue

        # previous_runs = previous_runs if previous_runs else None
        previous_runs = previous_runs if previous_runs else []

        if last_story:
            # Get user's subscription if needed for tab visibility
            subscription = await get_active_subscription(db, user_id)
            last_story = create_public_story_response(last_story, subscription)

        # Build homepage response
        logger.info("ℹ️ℹ️ℹ️ Building homepage response...")
        homepage_data = UserHomePageResponse(
            user_id=user.user_id,
            auth_email=user.auth_email,
            first_name=user.first_name,
            last_name=user.last_name,
            # name=user.name,
            company_name=user.company_name,
            business_email=user.business_email,
            last_generated_story=[] if last_story is None else last_story,
            credits_left=await get_user_credits(db, user_id),
            previous_story_details=previous_runs
        )

        logger.info("✅✅✅ Homepage data fetched successfully")
        return CustomDataResponse(
            status_code=status.HTTP_200_OK,
            detail="Homepage data retrieved successfully",
            data=homepage_data
        )

    except ValidationError as e:
        logger.error(f"❌❌❌ Validation error: {e}")
        raise HTTPException(
            status_code=status.HTTP_422_UNPROCESSABLE_ENTITY,
            detail=str(e)
        )
    except SQLAlchemyError as e:
        logger.error(f"❌❌❌ Database error: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Database error occurred"
        )
    except Exception as e:
        logger.error(f"❌❌❌ Unexpected error: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"An unexpected error occurred: {str(e)}"
        )




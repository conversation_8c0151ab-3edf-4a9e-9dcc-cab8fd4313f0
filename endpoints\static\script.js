async function ensureCustomerExists() {
  try {
    const response = await fetch("/payment/create-customer", {
      method: "POST",
      headers: {
        "Content-Type": "application/json"
      },
      body: JSON.stringify({
        user_id: currentUserId
      })
    });

    if (!response.ok) {
      const errorData = await response.json();
      console.error('Customer creation failed:', errorData);
      return false;
    }

    return true;
  } catch (error) {
    console.error('Error creating customer:', error);
    return false;
  }
}

let isProcessing = false;

async function createCheckoutSession(priceId) {
  if (isProcessing) return;
  
  try {
    isProcessing = true;
    document.getElementById('error-message').classList.remove('visible');
    
    // Show loading state
    const button = document.querySelector(`button[data-price-id="${priceId}"]`);
    const originalText = button.textContent;
    button.textContent = 'Processing...';
    button.disabled = true;

    // Ensure customer exists before creating checkout session
    const customerExists = await ensureCustomerExists();
    if (!customerExists) {
      throw new Error('Failed to create customer');
    }

    const response = await fetch("/payment/create-checkout-session", {
      method: "POST",
      headers: {
        "Content-Type": "application/json"
      },
      body: JSON.stringify({
        price_id: priceId,
        user_id: currentUserId
      })
    });

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.detail || 'Failed to create checkout session');
    }

    const session = await response.json();
    
    // Redirect to Stripe checkout
    const result = await stripe.redirectToCheckout({
      sessionId: session.sessionId
    });

    if (result.error) {
      throw new Error(result.error.message);
    }
  } catch (error) {
    console.error('Error:', error);
    const errorMessage = document.getElementById('error-message');
    errorMessage.textContent = error.message || 'An unexpected error occurred';
    errorMessage.classList.add('visible');
  } finally {
    isProcessing = false;
    // Reset button state
    const button = document.querySelector(`button[data-price-id="${priceId}"]`);
    if (button) {
      button.textContent = originalText;
      button.disabled = false;
    }
  }
}

const POWER_PRICE_ID = "price_1R2xcy02BL0Rwhwj77BxxjRz";
const GROWTH_PRICE_ID = "price_1R2xgW02BL0RwhwjiOQOJHy7";
const stripe = Stripe("pk_test_51R2Pxs02BL0Rwhwj9ifCmExN5NtjjQVpilWE5HCKng3tKUq69jN8LQrdMJFMiXHrYjhJaLEW1NthOGpMBL0bhuuh006kis7U8F");

// Helper function to handle portal session creation and redirect
async function redirectToPortalSession(buttonElement) {
    if (isProcessing) return; // Prevent double clicks

    const originalText = buttonElement.textContent;
    const errorMessageElement = document.getElementById('error-message');

    try {
        isProcessing = true;
        errorMessageElement.classList.remove('visible');
        buttonElement.disabled = true;
        buttonElement.textContent = 'Redirecting...';

        const response = await fetch("/payment/create-portal-session", {
            method: "POST",
            headers: {
                "Content-Type": "application/json"
            },
            // Ensure currentUserId is defined globally in your HTML template <script> tag
            body: JSON.stringify({
                user_id: currentUserId
            })
        });

        if (!response.ok) {
            const errorData = await response.json();
            throw new Error(errorData.detail || 'Failed to create portal session');
        }

        const { url } = await response.json();
        if (url) {
            window.location.href = url;
            // No need to reset button state if redirecting successfully
        } else {
            throw new Error('No redirect URL received from server.');
        }

    } catch (error) {
        console.error('Error creating portal session:', error);
        errorMessageElement.textContent = error.message;
        errorMessageElement.classList.add('visible');
        // Reset button state on error
        buttonElement.disabled = false;
        buttonElement.textContent = originalText;
    } finally {
        // Only set isProcessing to false if there was an error,
        // otherwise the page redirects.
        if (errorMessageElement.classList.contains('visible')) {
             isProcessing = false;
        }
    }
}

document.addEventListener("DOMContentLoaded", function() {
  const growthButton = document.getElementById("checkout-growth");
  if (growthButton) {
    growthButton.addEventListener("click", function() {
      redirectToPortalSession(this);
    });
  }

  const powerButton = document.getElementById("checkout-power");
  if (powerButton) {
    powerButton.addEventListener("click", function() {
      redirectToPortalSession(this);
    });
  }

  document.getElementById("manage-billing")?.addEventListener("click", function() {
    redirectToPortalSession(this);
  });

  // Only keep the gradient animation on mouse move
  document.addEventListener('mousemove', function(e) {
    const mouseX = e.clientX / window.innerWidth;
    const mouseY = e.clientY / window.innerHeight;
    
    const buttons = document.querySelectorAll('.button, .help-link');
    buttons.forEach(button => {
      const rect = button.getBoundingClientRect();
      const btnCenterX = rect.left + rect.width / 2;
      const btnCenterY = rect.top + rect.height / 2;
      
      const distanceX = (e.clientX - btnCenterX) / (window.innerWidth / 2) * 5;
      const distanceY = (e.clientY - btnCenterY) / (window.innerHeight / 2) * 5;
      
      if (Math.abs(distanceX) < 20 && Math.abs(distanceY) < 20) {
        button.style.backgroundPosition = `${50 + distanceX}% ${50 + distanceY}%`;
      }
    });
  });

  // <<< START: Add-on Purchase Logic >>>

  // Function to handle add-on checkout creation
  async function createAddonCheckout(priceId, buttonElement) {
    if (isProcessing) return; // Prevent double clicks

    const originalText = buttonElement.textContent;
    const errorMessageElement = document.getElementById('error-message');

    try {
      isProcessing = true;
      errorMessageElement.classList.remove('visible');
      buttonElement.disabled = true;
      buttonElement.textContent = 'Processing...';
      buttonElement.classList.add('loading'); // Add loading class

      const response = await fetch("/payment/create-checkout-session-addon", {
        method: "POST",
        headers: {
          "Content-Type": "application/json"
        },
        body: JSON.stringify({
          price_id: priceId,
          user_id: currentUserId // Assumes currentUserId is globally available
        })
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.detail || 'Failed to create add-on checkout session');
      }

      const session = await response.json();

      // Redirect to Stripe checkout for one-time payment
      const result = await stripe.redirectToCheckout({
        sessionId: session.sessionId
      });

      if (result.error) {
        // If redirectToCheckout fails, show error
        throw new Error(result.error.message);
      }
      // If redirect is successful, browser navigates away.

    } catch (error) {
      console.error('Add-on Purchase Error:', error);
      errorMessageElement.textContent = error.message;
      errorMessageElement.classList.add('visible');
      // Reset button state only on error
      buttonElement.disabled = false;
      buttonElement.textContent = originalText;
      buttonElement.classList.remove('loading');
      isProcessing = false; // Allow retry on error
    }
    // No finally block setting isProcessing=false if redirect happens
  }

  // Add event listeners to all purchase buttons
  const purchaseButtons = document.querySelectorAll('.purchase-addon-button');
  purchaseButtons.forEach(button => {
    button.addEventListener('click', function() {
      const priceId = this.getAttribute('data-price-id');
      if (priceId) {
        createAddonCheckout(priceId, this);
      } else {
        console.error("Could not find price ID on button:", this);
        const errorMessageElement = document.getElementById('error-message');
        errorMessageElement.textContent = "Cannot initiate purchase: price ID missing.";
        errorMessageElement.classList.add('visible');
      }
    });
  });
  // <<< END: Add-on Purchase Logic >>>
});
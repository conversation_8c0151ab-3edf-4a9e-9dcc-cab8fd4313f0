# import uvloop
import asyncio
import uvicorn
import logging
import os
import stripe
import uvloop

from endpoints.auth.auth_service import AuthService
from endpoints.core.app_factory import AppFactory
from endpoints.core.config import AppConfig
from endpoints.core.config_loader import Config<PERSON>oa<PERSON>
from endpoints.util.secrets import Secrets<PERSON><PERSON>
from endpoints.util.setup_gcp_logging import setup_gcp_logging

# Set uvloop as the event loop policy
asyncio.set_event_loop_policy(uvloop.EventLoopPolicy())
setup_gcp_logging()

app_config: AppConfig = ConfigLoader().load()
# SecretsClient().initialize()

stripe.api_key = os.getenv("STRIPE_API_KEY")
auth_service = AuthService()

app = AppFactory(app_config).create_app(auth_service)

# @app.exception_handler(StarletteHTTPException)
# async def custom_404(request: Request, exc: StarletteHTTPException):
#     from static import static_404
#     if exc.status_code == 404:
#         html = static_404.response_404
#         return HTMLResponse(content=html, status_code=404)
#     return JSONResponse(status_code=exc.status_code, content={"detail": exc.detail})

if __name__ == "__main__":
    uvicorn.run(app)

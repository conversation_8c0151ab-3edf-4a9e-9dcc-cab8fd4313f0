import os
from sqlalchemy.ext.asyncio import AsyncSession, create_async_engine
from sqlalchemy.orm import sessionmaker
from typing import AsyncGenerator
import logging


class DatabaseSessionManager:
    """Manages async database sessions using SQLAlchemy with connection pooling."""

    _DATABASE_URL = os.getenv("DATABASE_URL")

    _engine = create_async_engine(
        _DATABASE_URL,
        echo=True,
        pool_size=10,
        max_overflow=20,
        future=True,
    )

    _session_factory = sessionmaker(
        _engine, class_=AsyncSession, expire_on_commit=False
    )

    @classmethod
    async def get_session(cls) -> AsyncGenerator[AsyncSession, None]:
        """
        Provides an async database session to be used as a dependency.

        Automatically closes the session after the request.
        """
        session = cls._session_factory()
        try:
            yield session
        finally:
            await session.close()

    @classmethod
    async def init_db(cls):
        from endpoints.models.base import Base

        logging.info("Initializing database...")
        """
        (Optional) Create all tables in the database.
        If tables already exist, this is a no-op. 
        Run this once at startup if you want code-based migrations or schema creation.
        """
        async with cls._engine.begin() as conn:
            # Synchronously run DDL for all tables known to Base.metadata
            await conn.run_sync(Base.metadata.create_all)
        try:
            async with cls._engine.begin() as conn:
                await conn.run_sync(Base.metadata.create_all)
            logging.info("Database initialized successfully.")
        except Exception as e:
            logging.error(f"Error initializing database: {e}")

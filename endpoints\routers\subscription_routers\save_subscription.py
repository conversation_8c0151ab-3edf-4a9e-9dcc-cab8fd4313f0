import uuid

from fastapi import APIRouter, status, Depends
from sqlalchemy.exc import IntegrityError, SQLAlchemyError
from sqlalchemy import update, select

from endpoints.core.config import Plans
from endpoints.models.sales_smartstory_plans import SalesSmartStoryPlan
from endpoints.models.sales_smartstory_subscriptions import SalesSmartStorySubscription
from fastapi import HTTPException
from sqlalchemy.ext.asyncio import AsyncSession
from endpoints.models.schemas import (
    SalesSmartStorySubscriptionCreate,
    CustomDataResponse,
)
from endpoints.models.users import User
from endpoints.util.api_responses_util import standard_responses
from endpoints.util.database import DatabaseSessionManager
import logging


logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


# Router Library
save_subscription_router = APIRouter(prefix="/subscription", tags=["Subscriptions"])


from uuid import uuid4
from sqlalchemy.exc import IntegrityError, SQLAlchemyError
from sqlalchemy import select, update

async def create_subscription(
    subscription_data: SalesSmartStorySubscriptionCreate,
    db: AsyncSession = Depends(DatabaseSessionManager.get_session),
    carry_over_credits: int | None = None,
):
    """
    Creates a new subscription record.

    Logic merged from the legacy `create_subscription` implementation:

    1. Verifies the user exists.
    2. Looks up and deactivates any currently-active subscription (unless the
       caller already provided `carry_over_credits`), and carries forward
       remaining credits when the previous plan is NOT the free tier.
    3. Determines section-override flags based on whether the target plan is
       free or paid.
    4. Updates the user record to reflect paid status, current plan, and
       provisioning.
    5. Delegates to `update_user_plan` so downstream caches stay in sync.
    6. Surfaces integrity / DB errors with granular HTTP responses.
    """

    try:
        # --- 0. Ensure User Exists -------------------------------------------------
        user_result = await db.execute(
            select(User).filter(User.user_id == subscription_data.user_id)
        )
        user_obj = user_result.scalars().first()
        if not user_obj:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="User not found",
            )

        # --- 1. Fetch Plan ---------------------------------------------------------
        plan_result = await db.execute(
            select(SalesSmartStoryPlan).filter(
                SalesSmartStoryPlan.plan_id == subscription_data.plan_id
            )
        )
        plan = plan_result.scalars().first()
        if not plan:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Plan not found",
            )

        # --- 2. Deactivate Prev. Subscription & Carry Credits (if needed) ----------
        if carry_over_credits is None:
            prev_sub_result = await db.execute(
                select(SalesSmartStorySubscription).filter(
                    SalesSmartStorySubscription.user_id == subscription_data.user_id,
                    SalesSmartStorySubscription.active_subscription == True,
                )
            )
            prev_sub = prev_sub_result.scalar_one_or_none()
            if prev_sub:
                carry_over_credits = (
                    prev_sub.available_credit
                    if str(prev_sub.plan_id) != str(Plans.Free.id)
                    else 0
                )
                await db.execute(
                    update(SalesSmartStorySubscription)
                    .where(
                        SalesSmartStorySubscription.subscription_id
                        == prev_sub.subscription_id
                    )
                    .values(active_subscription=False)
                )
                logger.info(
                    f"Deactivated previous subscription "
                    f"{prev_sub.subscription_id}; carry-over credits: {carry_over_credits}"
                )

        # --- 3. Determine Final Available Credits ----------------------------------
        final_available_credits = (
            carry_over_credits
            if carry_over_credits is not None and carry_over_credits > 0
            else plan.plan_credit
        )

        # --- 4. Section Override Flags --------------------------------------------
        # todo fetch from plans table instead of hardcoding

        is_paid_plan = str(plan.plan_id) != str(Plans.Free.id)

        override_values = {
            "override_executive_summary": plan.executive_summary,
            "override_profile_poc": plan.profile_poc,
            "override_prospect_personality_profile": plan.prospect_personality_profile,
            "override_company_overview": plan.company_overview,
            "override_department_agency_overview": plan.department_agency_overview,

            "override_news": plan.news,
            "override_gov_news": plan.gov_news,
            "override_news_scoop": plan.news_scoop,
            "override_youtube_and_videos_mentions": plan.youtube_and_videos_mentions,
            "override_industry_trends": plan.industry_trends,

            "override_department_challenges": plan.department_challenges,
            "override_industry_challenges": plan.industry_challenges,
            "override_solution_overview": plan.solution_overview,
            "override_case_studies": plan.case_studies,
            "override_comp_intel": plan.comp_intel,
            "override_buying_guide": plan.buying_guide,
            "override_value_prop": plan.value_prop,
            "override_hyper_messaging": plan.hyper_messaging,
            "override_bant_assessment": plan.bant_assessment,
        }

        # --- 5. Create New Subscription -------------------------------------------
        new_subscription = SalesSmartStorySubscription(
            subscription_id=subscription_data.subscription_id or uuid4(),
            user_id=subscription_data.user_id,
            plan_id=subscription_data.plan_id,
            monthly_subscribed_credit=plan.plan_credit,
            available_credit=final_available_credits,
            transaction_id=subscription_data.transaction_id,
            transaction_date=subscription_data.transaction_date,
            active_subscription=True,
            current_period_start=subscription_data.current_period_start,
            current_period_end=subscription_data.current_period_end,
            **override_values,
        )

        db.add(new_subscription)
        await db.flush([new_subscription])

        # --- 6. Update User --------------------------------------------------------
        await db.execute(
            update(User)
            .where(User.user_id == subscription_data.user_id)
            .values(
                is_paid_user=is_paid_plan,
                plan_id=subscription_data.plan_id,
                is_user_provisioned=True,
            )
        )

        # --- 7. Commit -------------------------------------------------------------
        await db.commit()
        await db.refresh(new_subscription)

        logger.info(
            f"Subscription {new_subscription.subscription_id} created successfully "
            f"for user {subscription_data.user_id}."
        )
        return new_subscription

    # ---------------------------- Error Handling -----------------------------------
    except IntegrityError as e:
        await db.rollback()
        detail = str(e.orig).lower()
        if "unique constraint" in detail:
            raise HTTPException(
                status_code=status.HTTP_409_CONFLICT,
                detail="Duplicate subscription (user/plan or subscription_id)",
            )
        if "foreign key" in detail:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Invalid user_id or plan_id",
            )
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"Integrity error: {e.orig}",
        )

    except SQLAlchemyError as e:
        await db.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Database error: {e}",
        )

    except HTTPException:
        # Re-raise so FastAPI can handle it
        raise

    except Exception as e:
        await db.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Unexpected error: {e}",
        )



@save_subscription_router.post(
    "/save_subscription/",
    response_model=CustomDataResponse[SalesSmartStorySubscriptionCreate],
    responses=standard_responses(
        error_403_description="Failed to create subscription. The returned object is None.",
        error_500_description="Database error occurred / Unexpected Error Occurred",
        error_422_description="Unexpected error fetching active subscription",
    ),
)
async def add_subscription(
    subscription_data: SalesSmartStorySubscriptionCreate,
    db: AsyncSession = Depends(DatabaseSessionManager.get_session),
):
    """
    Handles the creation of a new subscription and saves it to the database.

    This function is an endpoint to accept subscription data, process its creation via
    the database, and return the created subscription. If successful, it logs the
    creation operation with relevant details.

    :param subscription_data: An instance of SalesSmartStorySubscriptionCreate containing
                              the required information for subscription to be created.
    :param db: The database session object retrieved through dependency injection.
    :return: The newly created subscription object on successful operation.
    """
    try:
        new_subscription = await create_subscription(subscription_data, db)
        if new_subscription:
            logger.info(
                f"✅✅✅ Subscription ➡️{new_subscription.subscription_id} ⬅️ created successfully."
            )
            new_subscription = SalesSmartStorySubscriptionCreate.model_validate(
                new_subscription
            )
            response = CustomDataResponse(
                status_code=status.HTTP_200_OK,
                detail=f"Subscription {new_subscription.subscription_id} created successfully",
                data=new_subscription,
            )
            return response
        else:
            logger.error("Failed to create subscription. The returned object is None.")
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Failed to create subscription. The returned object is None.",
            )

    except ValueError as e:
        logger.error("❌❌❌Unexpected error fetching active subscription", exc_info=e)
        raise HTTPException(
            status_code=status.HTTP_422_UNPROCESSABLE_ENTITY,
            detail="Unexpected error fetching active subscription.",
        )
    except Exception as e:
        logger.error(f"❌❌❌ Error creating subscription: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error while creating subscription: {e}",
        )
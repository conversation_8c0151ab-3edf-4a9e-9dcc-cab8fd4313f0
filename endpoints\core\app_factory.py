from __future__ import annotations

import importlib
import logging
import os
from typing import Union, Sequence

from fastapi import FastAPI, Depends
from fastapi.security import OAuth2PasswordBearer

from endpoints.auth.auth_service import AuthService
from endpoints.core.config import AppConfig
from endpoints.routers.auth.login import login_router
from endpoints.routers.payments.stripe import payment_router, public_payment_router
from endpoints.routers.referal_routers.referral import referral_router
from endpoints.routers.root import root_router
from endpoints.routers.user.delete_user import remove_user_router
from endpoints.routers.user.generate_story_request import generate_story_router

# from endpoints.routers.user.register import user_register_router
from endpoints.routers.user.save_user import save_user_router
from endpoints.routers.user.validate import validation_router
from endpoints.routers.user.get_story import get_story_router
from endpoints.routers.user.homepage import homepage_router
from endpoints.routers.user.get_prospect_list import prospect_router
from fastapi.templating import Jinja2Templates
from fastapi.staticfiles import StaticFiles
from endpoints.routers.subscription_routers.get_subscription import (
    get_subscription_router,
)
from endpoints.routers.subscription_routers.save_subscription import (
    save_subscription_router,
)
import logging

logger = logging.getLogger(__name__)

class AppFactory:
    def __init__(self, app_config: AppConfig):
        self.docs_url = app_config.docs_url
        self.redocs_url = app_config.redocs_url

    """Encapsulates FastAPI app creation and setup"""

    @staticmethod
    def _get_version():
        with open(
                os.path.join(os.path.dirname(__file__), "..", "..", "version")
        ) as version_file:
            return version_file.read().strip()

    def create_app(self, auth_service: AuthService) -> FastAPI:
        app = FastAPI(
            title="ProspectIntel API",
            description="Prospect Intelligence API to generate smart stories",
            version=self._get_version(),
            docs_url=self.docs_url,
            redoc_url=self.redocs_url,
            # openapi_url=self.openapi_url,
            openapi_tags=[
                {
                    "name": "User",
                    "description": "Operations with users.",
                },
                {
                    "name": "Auth",
                    "description": "Authentication operations.",
                },
                # Add other tags as needed
            ],
            openapi_components={
                "securitySchemes": {
                    "BearerAuth": {
                        "type": "http",
                        "scheme": "bearer",
                        "bearerFormat": "JWT",
                    }
                }
            },
            # Apply security globally
            security=[{"BearerAuth": []}],
        )

        # templates = Jinja2Templates(directory="templates")
        # app.dependency_overrides[Jinja2Templates] = lambda: templates
        app.mount("/static", StaticFiles(directory="endpoints/static"), name="static")
        # todo remove
        app = self.add_cors(app)

        self.include_routers(app, auth_service)
        return app

    def add_cors(self, app: FastAPI):
        from fastapi.middleware.cors import CORSMiddleware

        # Allow all CORS origins for development (Adjust in production!)

        app.add_middleware(
            CORSMiddleware,
            allow_origins=[
                "*"
            ],  # <--- Or specify allowed origins like ["http://localhost:3000"]
            allow_credentials=True,
            allow_methods=["*"],
            allow_headers=["*"],
        )
        return app

    @staticmethod
    def include_routers(app: FastAPI, auth_service: AuthService) -> FastAPI:
        # Public routes - no auth required
        public_routers = [root_router, save_user_router, public_payment_router]
        for router in public_routers:
            # Include without auth dependency
            app.include_router(router)
            logger.info(f"Included PUBLIC router: {router.prefix}")

        # Protected routes - require authentication
        protected_routers = [
            referral_router,
            save_subscription_router,
            validation_router,
            login_router,
            homepage_router,
            get_story_router,
            generate_story_router,
            prospect_router,
            get_subscription_router,
            payment_router
        ]

        auth_dependency = [Depends(auth_service.verify_access_token_dependency)]
        for router in protected_routers:
            # Include with auth dependency
            app.include_router(router, dependencies=auth_dependency )

            logger.info(f"Included PROTECTED router: {router.prefix}")

        return app

from dataclasses import dataclass, field
from typing import Optional


@dataclass
class CloudSQLConfig:
    """
    A configuration class that stores parameters required for connecting to a PostgreSQL database.

    Attributes:
        project_id (str): Google Cloud project ID (only for Cloud SQL).
        region (str): The region where the Cloud SQL instance is hosted (only for Cloud SQL).
        database (str): The database name inside the Cloud SQL instance.
        instance (str): The instance name of Cloud SQL (only for Cloud SQL).
        table_name (str): The default table to interact with.
    """

    project_id: str
    region: str
    database: str
    instance: str
    table_name: str


class Plan:
    def __init__(self, name: str, id: str, credit: int):
        self.name = name
        self.id = id
        self.credit = credit

class Plans:
    Free: Plan = Plan("free", "f47ac10b-58cc-4372-a567-0e02b2c3d479", 5)
    Power: Plan = Plan("power", "550e8400-e29b-41d4-a716-446655440000", 10)
    Growth: Plan = Plan("growth", "6ba7b810-9dad-11d1-80b4-00c04fd430c8", 10)



@dataclass
class AuthServiceConfig:
    jwt_access_expires_minutes: int = 60
    jwt_refresh_expires_minutes: int = 1440
    algorithm: str = "HS256"
    token_url: Optional[str] = "/auth/login"


@dataclass
class StripeConfig:
    signature_header_key: str = "stripe-signature"


@dataclass
class AppConfig:
    frontend_url: str
    backend_base_url: str
    templates_dir: str
    support_email: str
    project_id: Optional[str] = None
    docs_url: Optional[str] = None
    redocs_url: Optional[str] = None
    debug: bool = False
    auth_service_config: AuthServiceConfig = field(default_factory=AuthServiceConfig)
    stripe_config: StripeConfig = field(default_factory=StripeConfig)

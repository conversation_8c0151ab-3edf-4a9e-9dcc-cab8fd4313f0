from sqlalchemy import (
    Column,
    String,
    Boolean,
    TIMESTAMP,
    ForeignKey,
    Numeric,
    Date,
    text, Integer,
    DateTime
)
from sqlalchemy.dialects.postgresql import UUID
import uuid

from endpoints.models.base import Base


class SalesSmartStorySubscription(Base):
    __tablename__ = "sales_smartstory_subscriptions"

    subscription_id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    user_id = Column(
        UUID(as_uuid=True),
        ForeignKey("users.user_id", ondelete="CASCADE"),
        nullable=False,
    )
    plan_id = Column(
        UUID(as_uuid=True),
        ForeignKey("sales_smartstory_plans.plan_id", ondelete="CASCADE"),
        nullable=False,
    )
    # active_status = Column(Boolean, default=False)
    monthly_subscribed_credit = Column(Numeric, default=0)
    available_credit = Column(Numeric, default=0)
    enterprise_plan_interest = Column(Boolean, default=False)
    crm_subscription_upgrade_sync_pending = Column(Boolean, default=False)
    active_subscription = Column(Boolean, default=True)
    transaction_id = Column(UUID(as_uuid=True))
    transaction_date = Column(Date)
    override_executive_summary = Column(Boolean, default=True)
    override_profile_poc = Column(Boolean, default=True)
    override_prospect_personality_profile = Column(Boolean, default=False)
    override_hyper_messaging = Column(Boolean, default=False)

    override_company_overview = Column(Boolean, default=True)
    override_department_agency_overview= Column(Boolean, default=True)
    override_department_challenges = Column(Boolean, default=True)
    override_news = Column(Boolean, default=True)
    override_news_scoop = Column(Boolean, default=True)
    override_gov_news = Column(Boolean, default=True)
    override_youtube_and_videos_mentions = Column(Boolean, default=True)

    override_financial_summary = Column(Boolean, default=True)
    
    override_industry_trends = Column(Boolean, default=True)
    override_industry_challenges = Column(Boolean, default=True)
    override_solution_overview = Column(Boolean, default=False)
    override_case_studies = Column(Boolean, default=False)
    override_comp_intel = Column(Boolean, default=False)
    override_buying_guide = Column(Boolean, default=False)
    override_value_prop = Column(Boolean, default=False)
    override_bant_assessment = Column(Boolean, default=False)
    created_at = Column(TIMESTAMP(timezone=True), server_default=text("NOW()"))
    updated_at = Column(TIMESTAMP(timezone=True), server_default=text("NOW()"), onupdate=text("NOW()"))

    # New fields for billing cycle
    current_period_start = Column(DateTime(timezone=True), nullable=True)
    current_period_end = Column(DateTime(timezone=True), nullable=True)
import pytest
import pytest_asyncio
import asyncio
from testcontainers.postgres import PostgresContainer
from sqlalchemy.ext.asyncio import create_async_engine, AsyncSession
from sqlalchemy.orm import sessionmaker
from endpoints.models.base import Base


@pytest.fixture(scope="session")
def event_loop():
    """
    A session-scoped event loop so testcontainers + pytest-asyncio cooperate.
    """
    loop = asyncio.new_event_loop()
    yield loop
    loop.close()


@pytest.fixture(scope="session")
def postgres_container():
    """
    Synchronous fixture that spins up a Postgres test container
    once for the entire session, then tears it down.
    """
    with PostgresContainer("postgres:16") as pg:
        yield pg


@pytest_asyncio.fixture(scope="session")
async def engine(postgres_container):
    """
    Async fixture that creates and yields a single AsyncEngine
    for the entire test session, pointing to the test container.
    """
    async_url = postgres_container.get_connection_url(driver="asyncpg")
    engine_ = create_async_engine(async_url, echo=True)
    yield engine_
    await engine_.dispose()


@pytest_asyncio.fixture(scope="function")
async def prepare_db(engine):
    """
    Drops and re-creates all tables before each test function, ensuring a clean DB.
    """
    async with engine.begin() as conn:
        await conn.run_sync(Base.metadata.drop_all)
        await conn.run_sync(Base.metadata.create_all)


@pytest_asyncio.fixture
async def async_session_factory(engine):
    """
    Provides a sessionmaker factory that can create new AsyncSession objects.
    We do *not* drop/create tables here; that's handled by prepare_db.
    """
    return sessionmaker(engine, expire_on_commit=False, class_=AsyncSession)

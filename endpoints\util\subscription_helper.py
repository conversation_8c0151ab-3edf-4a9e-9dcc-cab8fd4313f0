import logging
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.future import select
from sqlalchemy.exc import SQLAlchemyError, IntegrityError
from uuid import UUID
from endpoints.models.sales_smartstory_subscriptions import SalesSmartStorySubscription

logger = logging.getLogger(__name__)


async def get_active_subscription(
    db: AsyncSession, user_id: UUID
) -> SalesSmartStorySubscription | None:
    """
    Get the active subscription for a user.

    Args:
        db: Database session
        user_id: UUID of the user

    Returns:
        SalesSmartStorySubscription if found, None otherwise

    Raises:
        SQLAlchemyError: When database operations fail
        ValueError: When input validation fails
        Exception: For unexpected errors
    """
    try:
        subscription_result = await db.execute(
            select(SalesSmartStorySubscription).filter(
                SalesSmartStorySubscription.user_id == user_id,
                SalesSmartStorySubscription.active_subscription == True,
            )
        )
        return subscription_result.scalar_one_or_none()
    except SQLAlchemyError as e:
        logger.error(
            f"❌❌❌ Database error while fetching subscription for user {user_id}: {e}"
        )
        await db.rollback()
        raise
    except IntegrityError as e:
        logger.error(
            f"❌❌❌ Integrity error while fetching subscription for user {user_id}: {e}"
        )
        await db.rollback()
        raise
    except ValueError as e:
        logger.error(
            f"❌❌❌ Validation error while fetching subscription for user {user_id}: {e}"
        )
        raise
    except Exception as e:
        logger.error(
            f"❌❌❌ Unexpected error while fetching subscription for user {user_id}: {e}"
        )
        await db.rollback()
        raise

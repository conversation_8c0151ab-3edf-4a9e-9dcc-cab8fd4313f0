
-- Insert data into users table
INSERT INTO users (user_id, auth_provider, firebase_uid, auth_email, first_name,last_name, company_name, business_email, crm_user_sync_pending, active_status, plan_id, stripe_customer_id) VALUES
('a0eebc99-9c0b-4ef8-bb6d-6bb9bd380a11', 'google', 'abc123def456ghi789jkl012mno3', '<EMAIL>', 'AI DEV2', 'AI User', 'Acme Inc', '<EMAIL>', FALSE, TRUE, '550e8400-e29b-41d4-a716-************', 'cus_SA5NjPO3PcuAIY'), -- Power plan
('a1eebc99-9c0b-4ef8-bb6d-6bb9bd380a12', 'microsoft', 'pqr678stu901vwx234yz567abc89', '<EMAIL>', 'Srikanth', '<PERSON>amon<PERSON>', 'Globex Corp', '<EMAIL>', FALSE, TRUE, '550e8400-e29b-41d4-a716-************', 'cus_SA5hhPFj6O9d5E'), -- Paid plan
('a2eebc99-9c0b-4ef8-bb6d-6bb9bd380a13', 'apple', 'ef345ghi678jkl901mno234pqr56', '<EMAIL>', 'Robert','Johnson', 'Initech', '<EMAIL>', TRUE, TRUE, '6ba7b810-9dad-11d1-80b4-00c04fd430c8', NULL), -- Paid plan
('a3eebc99-9c0b-4ef8-bb6d-6bb9bd380a14', 'google', 'ghi890jkl123mno456pqr789stu0', '<EMAIL>', 'Sarah','Williams', 'Umbrella Corp', '<EMAIL>', FALSE, TRUE, '6ba7b811-9dad-11d1-80b4-00c04fd430c8', NULL), -- Paid plan
('a4eebc99-9c0b-4ef8-bb6d-6bb9bd380a15', 'microsoft', 'jkl345mno678pqr901stu234vwx5', '<EMAIL>', 'Michael','Brown', 'Stark Industries', '<EMAIL>', TRUE, FALSE, '6ba7b812-9dad-11d1-80b4-00c04fd430c8', NULL), -- Paid plan
('a5eebc99-9c0b-4ef8-bb6d-6bb9bd380a16', 'microsoft', 'jkl345mno678pqr901stu234vwx6', '<EMAIL>', 'Michael','Yellow', 'Star Industries', '<EMAIL>', TRUE, FALSE, NULL, NULL), -- Paid plan
('a4a10359-967b-4b9b-acc3-08be576d67e8', 'google', '55a9bafcdf2b3096e8262caf414f', '<EMAIL>', 'John','Doe', 'Acme Corp', '<EMAIL>', FALSE, TRUE, NULL, NULL); -- Paid plan

-- Insert data into referrals table
INSERT INTO referrals (referral_id, user_id, crm_referral_sync_pending, referral_email) VALUES
('b0eebc99-9c0b-4ef8-bb6d-6bb9bd380a11', 'a0eebc99-9c0b-4ef8-bb6d-6bb9bd380a11', FALSE, '<EMAIL>'),
('b1eebc99-9c0b-4ef8-bb6d-6bb9bd380a12', 'a0eebc99-9c0b-4ef8-bb6d-6bb9bd380a11', TRUE, '<EMAIL>'),
('b2eebc99-9c0b-4ef8-bb6d-6bb9bd380a13', 'a1eebc99-9c0b-4ef8-bb6d-6bb9bd380a12', FALSE, '<EMAIL>'),
('b3eebc99-9c0b-4ef8-bb6d-6bb9bd380a14', 'a2eebc99-9c0b-4ef8-bb6d-6bb9bd380a13', TRUE, '<EMAIL>'),
('b4eebc99-9c0b-4ef8-bb6d-6bb9bd380a15', 'a3eebc99-9c0b-4ef8-bb6d-6bb9bd380a14', FALSE, '<EMAIL>');

-- Insert data into sales_smartstory_subscriptions table
INSERT INTO sales_smartstory_subscriptions (subscription_id, user_id, plan_id, active_status, monthly_subscribed_credit, available_credit, enterprise_plan_interest, crm_subscription_upgrade_sync_pending, active_subscription, transaction_id, transaction_date) VALUES
('c0eebc99-9c0b-4ef8-bb6d-6bb9bd380a11', 'a0eebc99-9c0b-4ef8-bb6d-6bb9bd380a11', 'f47ac10b-58cc-4372-a567-0e02b2c3d479', FALSE, 50, 50, FALSE, FALSE, FALSE, 'd0eebc99-9c0b-4ef8-bb6d-6bb9bd380a11', '2023-01-15'),
('c1eebc99-9c0b-4ef8-bb6d-6bb9bd380a12', 'a1eebc99-9c0b-4ef8-bb6d-6bb9bd380a12', '550e8400-e29b-41d4-a716-************', TRUE, 50, 35, FALSE, FALSE, TRUE, 'd1eebc99-9c0b-4ef8-bb6d-6bb9bd380a12', '2023-02-20'),
('c2eebc99-9c0b-4ef8-bb6d-6bb9bd380a13', 'a2eebc99-9c0b-4ef8-bb6d-6bb9bd380a13', '6ba7b810-9dad-11d1-80b4-00c04fd430c8', TRUE, 100, 75, FALSE, TRUE, TRUE, 'd2eebc99-9c0b-4ef8-bb6d-6bb9bd380a13', '2023-03-10'),
('c3eebc99-9c0b-4ef8-bb6d-6bb9bd380a14', 'a3eebc99-9c0b-4ef8-bb6d-6bb9bd380a14', 'f47ac10b-58cc-4372-a567-0e02b2c3d479', TRUE, 200, 150, FALSE, FALSE, TRUE, 'd3eebc99-9c0b-4ef8-bb6d-6bb9bd380a14', '2023-04-05'),
('c4eebc99-9c0b-4ef8-bb6d-6bb9bd380a15', 'a4eebc99-9c0b-4ef8-bb6d-6bb9bd380a15', '550e8400-e29b-41d4-a716-************', FALSE, 500, 450, TRUE, TRUE, FALSE, 'd4eebc99-9c0b-4ef8-bb6d-6bb9bd380a15', '2023-05-12'),
('c4eebc99-9c0b-4ef8-bb6d-6bb9bd380a16', 'a4a10359-967b-4b9b-acc3-08be576d67e8', '6ba7b810-9dad-11d1-80b4-00c04fd430c8', TRUE, 500, 450, TRUE, TRUE, TRUE, 'd4eebc99-9c0b-4ef8-bb6d-6bb9bd380a15', '2023-05-12'),
('c5eebc99-9c0b-4ef8-bb6d-6bb9bd380a17', 'a0eebc99-9c0b-4ef8-bb6d-6bb9bd380a11', '6ba7b810-9dad-11d1-80b4-00c04fd430c8', TRUE, 50, 50, FALSE, FALSE, TRUE, 'd5eebc99-9c0b-4ef8-bb6d-6bb9bd380a17', '2023-06-01');

-- Insert data into jobs table
INSERT INTO jobs (user_id, subscription_id) VALUES
('a0eebc99-9c0b-4ef8-bb6d-6bb9bd380a11', 'c0eebc99-9c0b-4ef8-bb6d-6bb9bd380a11'),
('a1eebc99-9c0b-4ef8-bb6d-6bb9bd380a12', 'c1eebc99-9c0b-4ef8-bb6d-6bb9bd380a12'),
('a2eebc99-9c0b-4ef8-bb6d-6bb9bd380a13', 'c2eebc99-9c0b-4ef8-bb6d-6bb9bd380a13'),
('a3eebc99-9c0b-4ef8-bb6d-6bb9bd380a14', 'c3eebc99-9c0b-4ef8-bb6d-6bb9bd380a14'),
('a4eebc99-9c0b-4ef8-bb6d-6bb9bd380a15', 'c4eebc99-9c0b-4ef8-bb6d-6bb9bd380a15');




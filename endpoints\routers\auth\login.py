import os
import uuid
from uuid import UUID

from fastapi import APIRouter, Depends, HTTPException, status, Body
from pydantic import BaseModel, EmailStr
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.future import select

from endpoints.models.schemas import TokenResponse, LoginRequest
from endpoints.models.users import User
from endpoints.util.api_responses_util import standard_responses
from endpoints.util.database import DatabaseSessionManager
from endpoints.auth.auth_service import AuthService

login_router = APIRouter(prefix="/auth", tags=["Auth"])


def get_auth_service():
    return AuthService(secret=os.getenv("JWT_SECRET"))


@login_router.post(
    "/login",
    response_model=TokenResponse,
    responses=standard_responses(
        error_401_description="Unauthorized: Invalid user credentials.",
        error_404_description="User not found.",
        error_500_description="Internal Server Error: Database error occurred / Unexpected error occurred.",
        error_422_description="Invalid input provided.",
    ),
)
async def login_user(
    login_data: LoginRequest,
    db: AsyncSession = Depends(DatabaseSessionManager.get_session),
    auth_service: AuthService = Depends(get_auth_service),
    security=None,
):
    # 1) Look up the user in the database.
    if security is None:
        security = []
    query = await db.execute(select(User).filter(User.auth_email == login_data.email))
    user = query.scalars().first()
    if not user:
        # If not found, raise HTTP 401 Unauthorized (or 404, as you prefer).
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="User not found with given email.",
        )

    # 2) If the user is found, generate an access token.
    user_payload = {
        "uid": str(user.user_id),
        "email": user.auth_email,
    }
    access_token = auth_service.generate_access_token(user_payload)

    # 3) Return the token in the response.
    return TokenResponse(
        access_token=access_token,
        user_id=user.user_id,
        is_user_provisioned=user.is_user_provisioned,
        is_user_locked_in=user.is_user_locked_in,
        user_plan=user.plan_id,
    )


@login_router.post("/refresh", summary="Refresh access token")
async def refresh_token(
    refresh_token: str = Body(..., embed=True),
    auth_service: AuthService = Depends(get_auth_service)
):
    try:
        # Verify the refresh token (allow_refresh=True to accept refresh tokens)
        token_data = auth_service.verify_token(refresh_token, allow_refresh=True)
        user_id = token_data.get("sub")  # 'sub' contains the user ID
        
        if not user_id:
            raise HTTPException(status_code=401, detail="Invalid refresh token")
            
        # Generate new access token using refresh_access_token method
        new_access_token = auth_service.refresh_access_token(refresh_token)
        
        return {"access_token": new_access_token}
    except HTTPException as e:
        raise e
    except Exception as e:
        raise HTTPException(status_code=401, detail="Invalid refresh token")

# Backend Endpoint Services Documentation

## Table of Contents
1. [Overview](#overview)
2. [How to Spin Up and Run the Application](#how-to-spin-up-and-run-the-application)
3. [Testing Endpoints with FastAPI](#testing-endpoints-with-fastapi)
4. [Testing Endpoints with Post<PERSON>](#testing-endpoints-with-postman)
5. [API Endpoints Reference](#api-endpoints-reference)

---

## Overview
This document provides comprehensive documentation for all backend endpoints, including instructions for running the application and testing endpoints using FastAPI and Postman. This format is suitable for Confluence or similar documentation platforms.

---

## How to Spin Up and Run the Application

### Prerequisites
- Python 3.8+
- [Poetry](https://python-poetry.org/docs/)
- Docker & Docker Compose (for database and dependencies)

### 1. Clone the Repository
```bash
git clone <your-repo-url>
cd backend-endpoint-services
```

### 2. Install Python Dependencies
```bash
poetry install
```

### 3. Start Database and Dependencies
```bash
docker-compose up -d
```

### 4. Run the Application
```bash
poetry run uvicorn endpoints.main:app --reload
```

- The API will be available at: `http://localhost:8000`
- FastAPI docs: `http://localhost:8000/docs`

---

## Testing Endpoints with FastAPI

1. Start the application as described above.
2. Open your browser and navigate to `http://localhost:8000/docs`.
3. Use the interactive Swagger UI to test all available endpoints. You can authorize, send requests, and view responses directly from the browser.

---

## Testing Endpoints with Postman

1. Start the application as described above.
2. Open Postman and create a new collection.
3. Set the base URL to `http://localhost:8000`.
4. Import the OpenAPI/Swagger spec from `http://localhost:8000/openapi.json` for automatic endpoint setup, or manually add requests as needed.
5. For endpoints requiring authentication, set the appropriate headers (e.g., `Authorization: Bearer <token>`).

---

## API Endpoints Reference

Below is a summary of the main endpoint groups. For full details, see the FastAPI docs at `/docs`.


### Auth Endpoints

#### `POST /auth/login`
**Description:** Authenticates a user and returns a JWT token.

**Request Body:**
```json
{
  "username": "string",
  "password": "string"
}
```

**Response:**
```json
{
  "access_token": "string",
  "token_type": "bearer"
}
```

**Errors:**
- 401 Unauthorized: Invalid credentials

---

#### `POST /auth/register` *(if implemented)*
**Description:** Registers a new user.

**Request Body:**
```json
{
  "username": "string",
  "email": "string",
  "password": "string"
}
```

**Response:**
```json
{
  "id": 1,
  "username": "string",
  "email": "string"
}
```

**Errors:**
- 400 Bad Request: Missing or invalid fields
- 409 Conflict: User already exists

---

#### `POST /auth/refresh` *(if implemented)*
**Description:** Refreshes the JWT token.

**Request Body:**
```json
{
  "refresh_token": "string"
}
```

**Response:**
```json
{
  "access_token": "string",
  "token_type": "bearer"
}
```

**Errors:**
- 401 Unauthorized: Invalid or expired refresh token

---

### User Endpoints

#### `GET /user/me`
**Description:** Retrieves the current user's profile.

**Headers:**
- Authorization: Bearer `<access_token>`

**Response:**
```json
{
  "id": 1,
  "username": "string",
  "email": "string",
  ...
}
```

**Errors:**
- 401 Unauthorized: Missing or invalid token

---

#### `PUT /user/me`
**Description:** Updates the current user's profile.

**Headers:**
- Authorization: Bearer `<access_token>`

**Request Body:**
```json
{
  "email": "string",
  "full_name": "string"
}
```

**Response:**
```json
{
  "id": 1,
  "username": "string",
  "email": "string",
  "full_name": "string"
}
```

**Errors:**
- 400 Bad Request: Invalid data
- 401 Unauthorized: Missing or invalid token

---

### Payments Endpoints

#### `POST /payments/stripe/charge`
**Description:** Creates a Stripe charge for the user.

**Headers:**
- Authorization: Bearer `<access_token>`

**Request Body:**
```json
{
  "amount": 1000,
  "currency": "usd",
  "source": "tok_visa"
}
```

**Response:**
```json
{
  "id": "ch_1...",
  "status": "succeeded",
  ...
}
```

**Errors:**
- 400 Bad Request: Invalid payment data
- 402 Payment Required: Payment failed
- 401 Unauthorized: Missing or invalid token

---

#### `GET /payments/stripe/history`
**Description:** Retrieves the payment history for the user.

**Headers:**
- Authorization: Bearer `<access_token>`

**Response:**
```json
[
  {
    "id": "ch_1...",
    "amount": 1000,
    "currency": "usd",
    "status": "succeeded",
    ...
  }
]
```

**Errors:**
- 401 Unauthorized: Missing or invalid token

---

### Referral Endpoints

#### `POST /referal_routers/referral`
**Description:** Creates a new referral.

**Headers:**
- Authorization: Bearer `<access_token>`

**Request Body:**
```json
{
  "email": "<EMAIL>"
}
```

**Response:**
```json
{
  "id": 1,
  "email": "<EMAIL>",
  "status": "pending"
}
```

**Errors:**
- 400 Bad Request: Invalid email
- 401 Unauthorized: Missing or invalid token

---

#### `GET /referal_routers/referral/{id}`
**Description:** Retrieves a referral by its ID.

**Headers:**
- Authorization: Bearer `<access_token>`

**Response:**
```json
{
  "id": 1,
  "email": "<EMAIL>",
  "status": "pending"
}
```

**Errors:**
- 404 Not Found: Referral not found
- 401 Unauthorized: Missing or invalid token

---

### Subscription Endpoints

#### `POST /subscription_routers/subscribe`
**Description:** Subscribes the user to a plan.

**Headers:**
- Authorization: Bearer `<access_token>`

**Request Body:**
```json
{
  "plan_id": "pro"
}
```

**Response:**
```json
{
  "subscription_id": 1,
  "plan_id": "pro",
  "status": "active"
}
```

**Errors:**
- 400 Bad Request: Invalid plan
- 401 Unauthorized: Missing or invalid token

---

#### `GET /subscription_routers/status`
**Description:** Retrieves the user's subscription status.

**Headers:**
- Authorization: Bearer `<access_token>`

**Response:**
```json
{
  "subscription_id": 1,
  "plan_id": "pro",
  "status": "active"
}
```

**Errors:**
- 404 Not Found: No active subscription
- 401 Unauthorized: Missing or invalid token

---

### Other Endpoints

#### `GET /`
**Description:** Health check or welcome endpoint.

**Response:**
```json
{
  "message": "Welcome to the API!"
}
```

---

> **Note:** For detailed request/response schemas, required fields, and error codes, refer to the FastAPI docs at `/docs` or `/redoc`.

---

## Additional Notes
- Environment-specific configuration files are located in `endpoints/config/dev/` and `endpoints/config/prod/`.
- Database initialization scripts are in `docker/init-scripts/`.
- For running tests: `poetry run pytest endpoints/tests/`

---

For further details, consult the `README.md` or contact the backend team.

import logging
import os


logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class EnvironmentUtils:

    @staticmethod
    def is_running_in_gcp(*env_vars):
        """
        Checks if the code is running inside a Google Cloud environment.

        If custom environment variables are provided, it only checks those.
        Otherwise, it checks the default GCP-specific environment variables.

        :param env_vars: Optional list of environment variable names to check.
        :return: True if any of the specified environment variables are set, False otherwise.
        """
        gcp_env_vars = (
            env_vars
            if env_vars
            else [
                "K_SERVICE",  # Cloud Run
                "FUNCTION_NAME",  # Cloud Functions
                "GAE_SERVICE",  # App Engine
            ]
        )

        return any(os.getenv(var) for var in gcp_env_vars)

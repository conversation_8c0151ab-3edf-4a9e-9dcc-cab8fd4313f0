import uuid
from sqlalchemy import Column, String, DateTime, <PERSON><PERSON><PERSON>, Integer
from sqlalchemy.dialects.postgresql import UUID
from sqlalchemy.sql import func

from endpoints.models.base import Base


class PromotionRedemptions(Base):
    __tablename__ = "promotion_redemptions"

    id = Column(Integer, primary_key=True, autoincrement=True)
    user_email = Column(String(255), nullable=False)
    promotion_id = Column(UUID(as_uuid=True), ForeignKey("promotions.promotion_id"), nullable=True)
    promo_code_used = Column(String(255), nullable=False)  # Stores the promo code the user registered with
    registered_at = Column(DateTime(timezone=True), server_default=func.now())

    def __repr__(self):
        return f"<UserPromotion(user_email={self.user_email}, promo_code={self.promo_code_used})>"

import jwt
from jwt import PyJWK<PERSON>lient, InvalidTokenError
import requests

GOOGLE_CERTS_URL = "https://www.googleapis.com/oauth2/v3/certs"
MICROSOFT_CERTS_URL = "https://login.microsoftonline.com/common/discovery/v2.0/keys"


def verify_google_token(token: str, google_client_id: str) -> dict:
    """
    Verifies a Google-issued ID token.
    - Checks the signature using Google's public keys.
    - Ensures the 'aud' (audience) matches our Google client ID.
    - Returns the decoded token (dict) if valid, otherwise raises an error.
    """
    jwk_client = PyJWKClient(GOOGLE_CERTS_URL)

    try:
        signing_key = jwk_client.get_signing_key_from_jwt(token)
        decoded_token = jwt.decode(
            token,
            signing_key.key,
            algorithms=["RS256"],
            audience=google_client_id,  # Must match your Google OAuth client ID
            options={"verify_exp": True},  # Ensure token is not expired
        )
        return decoded_token
    except (InvalidTokenError, KeyError) as e:
        raise ValueError(f"Invalid Google token: {str(e)}")


def verify_microsoft_token(token: str, microsoft_client_id: str) -> dict:
    """
    Verifies a Microsoft-issued ID token.
    - Checks the signature using Microsoft’s public keys.
    - Ensures the 'aud' (audience) matches our Microsoft client ID.
    - Returns the decoded token (dict) if valid, otherwise raises an error.
    """
    jwk_client = PyJWKClient(MICROSOFT_CERTS_URL)

    try:
        signing_key = jwk_client.get_signing_key_from_jwt(token)
        decoded_token = jwt.decode(
            token,
            signing_key.key,
            algorithms=["RS256"],
            audience=microsoft_client_id,  # Must match your Azure AD / Microsoft client ID
            options={"verify_exp": True},
        )
        return decoded_token
    except (InvalidTokenError, KeyError) as e:
        raise ValueError(f"Invalid Microsoft token: {str(e)}")

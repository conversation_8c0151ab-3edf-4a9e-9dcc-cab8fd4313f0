import os
from sqlalchemy.ext.asyncio import AsyncEngine, create_async_engine
from sqlalchemy.sql import text
from google.cloud.sql.connector import create_async_connector
from typing import Dict, List, Optional, Union, Callable
import asyncpg

from endpoints.core.config import CloudSQLConfig
from endpoints.util.environment_utils import EnvironmentUtils


# not being used in the codebase
class AsyncCloudSQLs:
    """
    Asynchronous manager for interacting with a PostgreSQL database.

    This class allows switching between local PostgreSQL and Google Cloud SQL
    by passing a callable function `is_local` that determines the mode.

    Attributes:
        cloudsql_config (CloudSQLConfig): Configuration object for Cloud SQL parameters.
        is_local (Callable[[], bool]): A function that determines if using local PostgreSQL.
        connector (Optional[asyncpg.Connection]): Connector for Cloud SQL (used only if `is_local()` returns False).
        pool (Optional[AsyncEngine]): Async SQLAlchemy engine for managing database connections.
    """

    def __init__(
        self,
        cloudsql_config: CloudSQLConfig,
        is_local: Callable[[], bool] = lambda: EnvironmentUtils().is_running_in_gcp(),
    ):
        """
        Initializes the AsyncCloudSQL manager with the given Cloud SQL configuration.

        Args:
            cloudsql_config (CloudSQLConfig): The configuration parameters for Cloud SQL.
            is_local (Callable[[], bool], optional): A callable function that returns True if using a local
                                                     PostgreSQL database. Defaults to [[EnvironmentUtils().is_running_in_gcp()]]`.
        """
        self.cloudsql_config = cloudsql_config
        self.is_local = is_local
        self.connector = None
        self.pool = None

    async def __connection_string__(self) -> str:
        """
        Constructs the Cloud SQL instance connection string.

        Returns:
            str: The formatted Cloud SQL instance connection string.
        """
        return f"{self.cloudsql_config.project_id}:{self.cloudsql_config.region}:{self.cloudsql_config.instance}"

    async def init_connection_pool(self) -> AsyncEngine:
        """
        Initializes the async connection pool for PostgreSQL.

        If `is_local()` returns True, it connects to a local PostgreSQL instance.
        Otherwise, it uses Google Cloud SQL via the Cloud SQL Connector.

        Returns:
            AsyncEngine: The async SQLAlchemy engine configured with the connection pool.
        """
        if self.is_local():
            print("Running in LOCAL mode: Connecting to local PostgreSQL database.")
            self.pool = create_async_engine(
                "postgresql+asyncpg://postgres:password@localhost/postgres",
                pool_size=5,
                max_overflow=2,
            )
        else:
            print("Running in CLOUD mode: Connecting to Google Cloud SQL instance.")

            if not self.connector:
                self.connector = await create_async_connector()

            user = os.environ["CLOUDSQL_USERNAME"]
            password = os.environ["CLOUDSQL_PASSWORD"]
            db = self.cloudsql_config.database

            async def getconn() -> asyncpg.Connection:
                """
                Creates an asyncpg connection using the Cloud SQL connector.

                Returns:
                    asyncpg.Connection: The asyncpg connection instance.
                """
                return await self.connector.connect_async(
                    instance_connection_string=await self.__connection_string__(),
                    driver="asyncpg",
                    user=user,
                    password=password,
                    db=db,
                )

            self.pool = create_async_engine(
                "postgresql+asyncpg://",
                async_creator=getconn,
                pool_size=5,
                max_overflow=2,
            )

        return self.pool

    async def insert(self, query: str, params: Union[Dict, List]) -> None:
        """Executes an asynchronous INSERT SQL statement."""
        async with self.pool.begin() as conn:
            await conn.execute(text(query), params)

    async def select(self, query: str, params: Optional[Dict] = None):
        """Executes an asynchronous SELECT SQL query and retrieves results."""
        async with self.pool.connect() as conn:
            result = await conn.execute(text(query), params or {})
            return result.fetchall()

    async def select_as_dict(
        self, query: str, params: Optional[Dict] = None, as_dict: bool = False
    ):
        """Executes a SELECT SQL query and returns results as dictionaries if requested."""
        async with self.pool.connect() as conn:
            result = await conn.execute(text(query), params or {})
            rows = result.fetchall()
            return [dict(row._mapping) for row in rows] if as_dict else rows

    async def delete(self, query: str, params: Optional[Dict] = None) -> None:
        """Executes an asynchronous DELETE SQL statement."""
        async with self.pool.begin() as conn:
            result = await conn.execute(text(query), params or {})
            print(f"{result.rowcount} row(s) deleted.")

    async def update(self, query: str, params: Dict) -> None:
        """Executes an asynchronous UPDATE SQL statement."""
        async with self.pool.begin() as conn:
            result = await conn.execute(text(query), params)
            print(f"{result.rowcount} row(s) updated.")

    async def close(self):
        """Closes the Cloud SQL connector and the database connection pool."""
        if self.connector:
            await self.connector.close_async()
        if self.pool:
            await self.pool.dispose()

    async def __aenter__(self):
        """
        Initializes the connection pool when entering the context.

        Returns:
            AsyncCloudSQL: The current instance with an initialized connection pool.
        """
        self.pool = await self.init_connection_pool()
        return self

    async def __aexit__(self, exc_type, exc_value, traceback):
        """Ensures the connection pool and connector are properly closed when exiting the context."""
        await self.close()

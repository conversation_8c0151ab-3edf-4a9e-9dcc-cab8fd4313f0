from fastapi.openapi.models import Response
from fastapi.openapi.utils import status_code_ranges
from pydantic import BaseModel, EmailStr, UUID4
from typing import Optional, Dict, List
from sqlalchemy.future import select
from sqlalchemy.exc import IntegrityError
from sqlalchemy.ext.asyncio import AsyncSession

from endpoints.models.schemas import CustomDataResponse
from endpoints.util.api_responses_util import standard_responses
from endpoints.util.database import DatabaseSessionManager
from endpoints.util.sql_injection_check import check_sql_injection_decorator
from fastapi import APIRouter, HTTPException, Depends, status, Security
from fastapi.security import HTT<PERSON><PERSON>earer, HTTPAuthorizationCredentials

from endpoints.models.referral import Referral
import unicodedata
import logging
from fastapi.security import OAuth2PasswordBearer

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

oauth2_scheme = OAuth2PasswordBearer(tokenUrl="/login")

# Router Library
referral_router = APIRouter(prefix="/referrals", tags=["Referrals"])
security = HTTPBearer()


class ReferralRequest(BaseModel):
    user_id: UUID4
    referral_email: List[EmailStr]


def debug_email(email: str):
    print(f"Original Email: {email}")
    print(f"Length of Email: {len(email)}")
    print(
        f"Normalized Email: {unicodedata.normalize('NFKC', email)}"
    )  # Normalize weird characters
    print(f"Encoded Email (utf-8): {email.encode('utf-8')}")
    print(f"Encoded Email (idna): {email.split('@')[1].encode('idna')}")


@referral_router.post(
    "/save_referral",
    response_model=CustomDataResponse[None],
    responses=standard_responses(
        error_208_description="All provided referrals were duplicate",
        error_400_description="One or more referral emails already exist.",
        error_500_description="Database error occurred / Unexpected Error Occurred",
        error_422_description="Invalid input provided.",
    ),
)
async def save_referral(
    referral: ReferralRequest,
    db: AsyncSession = Depends(DatabaseSessionManager.get_session),
    # credentials: HTTPAuthorizationCredentials = Security(security),
) -> CustomDataResponse | Response:
    try:
        logger.info(f"Received referral emails: {referral.referral_email}")

        new_referrals = []
        for referral_email in referral.referral_email:
            # Check if the referral_email already exists
            existing_referral = await db.execute(
                select(Referral).where(Referral.referral_email == referral_email)
            )
            if existing_referral.scalars().first():
                logger.info(
                    f"⏩⏩⏩ Duplicate email {referral_email} found. Skipping insertion."
                )
                continue

            # Create and add the new referral
            referral_obj = Referral(
                user_id=referral.user_id, referral_email=referral_email
            )
            db.add(referral_obj)
            new_referrals.append(referral_email)

        if new_referrals:
            await db.commit()
            logger.info(f"New referrals saved successfully: {new_referrals}")
            response = CustomDataResponse(
                status_code=status.HTTP_200_OK,
                detail="New Referrals Saved Successfully",
                data=None,
            )
            return response
        else:
            logger.warning("No new referrals to save.")
            return CustomDataResponse(
                status_code=status.HTTP_208_ALREADY_REPORTED,
                detail="No new referrals to save. All provided emails were duplicates.",
                data=None,
            )
    except IntegrityError as e:
        logger.error(f"Integrity error: {e}")
        await db.rollback()
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="One or more referral emails already exist.",
        )
    except ValueError as e:
        logger.error(f"ValueError: {e}")
        await db.rollback()
        raise HTTPException(
            status_code=status.HTTP_422_UNPROCESSABLE_ENTITY,
            detail=f"Invalid input: {e}",
        )

    except Exception as e:
        logger.error(f"Unexpected error: {e}")
        await db.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"An unexpected error occurred. Detail: {e}",
        )

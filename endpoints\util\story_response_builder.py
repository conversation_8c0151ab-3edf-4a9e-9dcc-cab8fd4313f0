from __future__ import annotations

import json
from dataclasses import dataclass
from typing import List, Optional

from endpoints.models.schemas import ProspectDetail, Tabs, PublicStoryResponse
from endpoints.models.stories import Story, RunStatus
from endpoints.models.sales_smartstory_subscriptions import (
    SalesSmartStorySubscription,
)

# --------------------------------------------------------------------------- #
# Prospect helpers
# --------------------------------------------------------------------------- #


def create_prospect_detail(story: Story) -> ProspectDetail:
    """Return a :class:`ProspectDetail` instance derived from *story*."""
    return ProspectDetail(
        prospect_first_name=(story.prospect_first_name or "").title(),
        prospect_last_name=(story.prospect_last_name or "").title(),
        email=story.prospect_email,
        company=(story.prospect_company or "").title(),
        title=(story.prospect_title or "").title(),
        prospect_linkedin=story.prospect_linkedin,
        prospect_company_industry=story.prospect_company_industry,
        prospect_company_url=story.prospect_company_url,
        profile_picture_url=story.profile_picture_url,
    )


# --------------------------------------------------------------------------- #
# Tab helpers
# --------------------------------------------------------------------------- #


def prettify(tab_id: str) -> str:
    """
    Convert a *snake_case* ``tab_id`` into a human‑friendly label.

    Acronyms and special‑cases are handled explicitly to preserve their
    capitalisation.
    """
    SPECIAL = {
        "poc": "POC",
        "bant": "BANT",
        "10k": "10‑K",
    }

    parts = tab_id.split("_")

    # Drop the leading "prospect" *only* for "prospect_company_overview"
    if parts[0] == "prospect" and parts[1] == "company":
        parts = parts[1:]

    return " ".join(SPECIAL.get(p.lower(), p.capitalize()) for p in parts)


@dataclass(frozen=True, slots=True)
class TabSpec:
    id: str  # attribute name on Story
    override_attr: str  # corresponding boolean on Subscription
    label: Optional[str] = None  # explicit label (falls back to prettify)


TAB_SPECS: List[TabSpec] = [
    TabSpec("executive_summary", "override_executive_summary"),
    TabSpec("prospect_poc", "override_profile_poc", "Profile"),
    TabSpec("prospect_personality_profile", "override_prospect_personality_profile", "Personality Profile"),
    TabSpec(
        "prospect_company_overview",
        "override_company_overview",
        "Company Overview",
    ),
    TabSpec("department_agency_overview", "override_department_agency_overview", "Agency Overview"),
    # TabSpec("department_challenges", "override_department_challenges"),
    TabSpec("financial_summary", "override_financial_summary", "Financial Summary"),
    TabSpec("industry_trends", "override_industry_trends"),
    TabSpec("news_scoop", "override_news"),
    TabSpec("gov_news", "override_news"),
    TabSpec("youtube_and_videos_mentions", "override_youtube_and_videos_mentions", "Social Media"),
    TabSpec("industry_challenges", "override_industry_challenges"),
    TabSpec("solution_overview", "override_solution_overview"),
    TabSpec("competitive_intelligence", "override_comp_intel", "Comp Intel"),
    TabSpec("case_studies", "override_case_studies"),
    TabSpec("buying_cycle", "override_buying_guide"),
    TabSpec("value_proposition", "override_value_prop"),
    TabSpec("hyper_messaging", "override_hyper_messaging"),
    TabSpec("bant_assessment", "override_bant_assessment", "BANT Assessment"),
]


def _tab_placeholder(message: str) -> str:
    """Return a standardized placeholder message."""
    return message


def get_tab_content(
    is_section_active: bool,
    section_content: Optional[str],
    status: RunStatus,
) -> str:
    """Return the text that should appear inside a tab."""
    if status is RunStatus.in_progress:
        return _tab_placeholder("SmartStory generation in progress…")

    if status is RunStatus.failed:
        return _tab_placeholder(
            "Failed to generate content. <NAME_EMAIL>."
        )

    if is_section_active:
        return (
            section_content
            or _tab_placeholder(
                "No content available. If this is a mistake, <NAME_EMAIL>."
            )
        )

    return _tab_placeholder("Please subscribe to this section to view content.")



def create_story_tabs(
    story: Story,
    subscription: Optional[SalesSmartStorySubscription] = None,
) -> List[Tabs]:
    """Build the list of: class:`Tabs` to return for *story*."""
    tabs: List[Tabs] = []

    # Check which overview exists in the story
    company_overview = getattr(story, "prospect_company_overview", None)
    department_overview = getattr(story, "department_agency_overview", None)

    news_scoop = getattr(story, "news_scoop", None)
    gov_news = getattr(story, "gov_news", None)

    for spec in TAB_SPECS:
        raw_content = getattr(story, spec.id, None)
        is_active = (
            True
            if subscription is None
            else getattr(subscription, spec.override_attr, False)
        )

        # Skip company overview if department overview exists
        if spec.id == "prospect_company_overview" and department_overview:
            continue
            
        # Skip department overview if company overview exists
        if spec.id == "department_agency_overview" and company_overview:
            continue

        # skip gov_news if news_scoop exists
        if spec.id == "gov_news" and news_scoop:
            continue

        # skip news_scoop if gov_news exists
        if spec.id == "news_scoop" and gov_news:
            continue

        tabs.append(
            Tabs(
                id=spec.id,
                name=spec.label or prettify(spec.id),
                content=get_tab_content(is_active, raw_content, story.status),
                is_active=is_active,
            )
        )

    # 10‑K Summary is always included if present on the Story
    ten_k = getattr(story, "10_k_summary", None)
    if ten_k:
        tabs.append(
            Tabs(
                id="10k_summary",
                name="10-K Summary",
                content=ten_k,
                is_active=True,
            )
        )

    return tabs


# --------------------------------------------------------------------------- #
# Public response
# --------------------------------------------------------------------------- #


def create_public_story_response(
    story: Story,
    subscription: Optional[SalesSmartStorySubscription] = None,
) -> PublicStoryResponse:
    """Return a fully‑populated :class:`PublicStoryResponse` for *story*."""
    frontend_summary = None
    if story.overall_summary:
        try:
            frontend_summary = json.loads(story.overall_summary)
            r = []

            for k, v in frontend_summary.items():
                r.append(k.replace('_', ' ').title()+': ')
                r.append(v+"\n\n")

            frontend_summary = "".join(r)
        except Exception:
            frontend_summary = story.overall_summary

    return PublicStoryResponse(
        run_id=story.run_id,
        user_id=story.user_id,
        status=story.status.value,
        title=(story.title or "").title(),
        # overall_summary=story.executive_summary,
        overall_summary=frontend_summary,
        prospect=create_prospect_detail(story),
        # profile_picture_url=story.profile_picture_url,
        # linkedin_url=story.prospect_linkedin,
        tabs=create_story_tabs(story, subscription),
        created_at=story.created_at,
        updated_at=story.updated_at,
    )

# def create_story_tabs(story: Story, subscription: SalesSmartStorySubscription = None) -> List[Tabs]:
#     """Create list of Tabs from Story"""
#     # If no subscription provided, assume all tabs are active
#     override_defaults = False if subscription is None else True
#
#     return [
#         Tabs(
#             id="prospect_poc",
#             name="Prospect POC",
#             content=story.prospect_poc,
#             is_active=(override_defaults and subscription.override_profile_poc)
#         ),
#         Tabs(
#             id="prospect_company_overview",
#             name="Company Overview",
#             content=story.prospect_company_overview,
#             is_active= (override_defaults and subscription.override_company_overview)
#         ),
#         Tabs(
#             id="industry_trends",
#             name="Industry Trends",
#             content=story.industry_trends,
#             is_active= (override_defaults and subscription.override_industry_trends)
#         ),
#         Tabs(
#             id="recent_news",
#             name="Recent News",
#             content=story.recent_news,
#             is_active= (override_defaults and subscription.override_news)
#         ),
#         Tabs(
#             id="industry_challenges",
#             name="Industry Challenges",
#             content=get_tab_content(subscription.override_industry_challenges,story.industry_challenges),
#             # content="" if subscription.override_industry_challenges else story.industry_challenges,
#             is_active= (override_defaults and subscription.override_industry_challenges)
#         ),
#         Tabs(
#             id="10k_summary",
#             name="10-K Summary",
#             content=getattr(story, '10_k_summary', None),
#             is_active=True #False if getattr(story, '10_k_summary', None) is None else True
#         ),
#         Tabs(
#             id="solution_overview",
#             name="Solution Overview",
#             content= get_tab_content(subscription.override_solution_overview,story.solution_overview),
#             # content="" if subscription.override_solution_overview else story.solution_overview,
#             is_active= (override_defaults and subscription.override_solution_overview)
#         ),
#         Tabs(
#             id="competitive_intelligence",
#             name="Competitive Intelligence",
#             content= get_tab_content(subscription.override_comp_intel,story.competitive_intelligence),
#             # content="" if subscription.override_comp_intel else story.competitive_intelligence,
#             is_active= (override_defaults and subscription.override_comp_intel)
#         ),
#         Tabs(
#             id="case_studies",
#             name="Case Studies",
#             content= get_tab_content(subscription.override_case_studies,story.case_studies),
#             # content="" if subscription.override_case_studies else story.case_studies,
#             is_active= (override_defaults and subscription.override_case_studies)
#         ),
#         Tabs(
#             id="buying_cycle",
#             name="Buying Cycle",
#             content=get_tab_content(subscription.override_buying_guide,story.buying_cycle),
#             # content="" if subscription.override_buying_guide else story.buying_cycle,
#             is_active= (override_defaults and subscription.override_buying_guide)
#         ),
#         Tabs(
#             id="value_proposition",
#             name="Value Proposition",
#             content=get_tab_content(subscription.override_value_prop,story.value_proposition),
# #             content="" if subscription.override_value_prop else story.value_proposition,
#             is_active= (override_defaults and subscription.override_value_prop)
#         ),
#         Tabs(
#             id="bant_assessment",
#             name="BANT Assessment",
#             content=get_tab_content(subscription.override_bant_assessment,story.bant_assessment),
# #             content="" if subscription.override_bant_assessment else story.bant_assessment,
#             is_active= (override_defaults and subscription.override_bant_assessment)
#         )
#     ]
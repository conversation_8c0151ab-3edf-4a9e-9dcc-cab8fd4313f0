from google.cloud import secretmanager
import json
import os
import json
import google.auth
import google.auth.impersonated_credentials

from google.cloud import secretmanager
from google.oauth2 import service_account
from google.auth.transport.requests import Request

import logging

logger = logging.getLogger(__name__)


class SecretsClient:

    def __get_secret_data__(self, name: str) -> dict:
        secret_manager_client = secretmanager.SecretManagerServiceClient()

        response = secret_manager_client.access_secret_version(
            request={"name": name}
        )
        payload = response.payload.data.decode("UTF-8")
        secret_data = json.loads(payload)

        return secret_data

    def initialize(self):
        env_var_secrets_path = os.getenv('ENVIRONMENT_VAR_SECRETS')
        env_var_secrets = self.__get_secret_data__(env_var_secrets_path)
        if not env_var_secrets:
            return

        keys = env_var_secrets.keys()

        try:
            for key in keys:
                os.environ[key] = env_var_secrets.get(key)
            logger.info("Loaded secrets as env variables")
        except Exception as e:
            logger.critical(f"Failed to load secrets with error {e}")
            raise

    def get_id_token_from_secret(self, secret_id: str, target_url: str) -> str:
        service_account_info = self.__get_secret_data__(secret_id)
        # Create credentials from the service account info
        credentials = service_account.IDTokenCredentials.from_service_account_info(
            service_account_info, target_audience=target_url
        )
        # Refresh to get a valid ID token
        credentials.refresh(Request())
        return credentials.token

    def get_id_token_via_impersonation(
            self, target_principal_email: str, target_audience: str
    ) -> str:
        """
        Generates an OIDC ID token by impersonating a target service account.

        Args:
            target_principal_email: The email address of the service account to impersonate.
            target_audience: The audience for the ID token (e.g., the URL of the receiving service).

        Returns:
            The OIDC ID token string.

        Raises:
            Exception: If token generation fails.
        """
        try:
            logger.debug(
                f"Attempting to generate ID token for audience '{target_audience}' "
                f"by impersonating '{target_principal_email}'."
            )
            # 1. Get the credentials of the current environment (e.g., Cloud Run service's SA)
            #    This SA needs 'Service Account Token Creator' role on the target_principal_email.
            source_creds, _ = google.auth.default(
                scopes=["https://www.googleapis.com/auth/cloud-platform"]
            )

            # 2. Create credentials that will impersonate the target_principal_email.
            #    These are 'access token' generating impersonated credentials.
            impersonated_creds = google.auth.impersonated_credentials.Credentials(
                source_credentials=source_creds,
                target_principal=target_principal_email,
                target_scopes=["https://www.googleapis.com/auth/cloud-platform"],  # General scope
                lifetime=300,  # Optional: lifetime of the impersonated access token in seconds
            )

            # 3. Now, use these impersonated_creds to get an ID Token.
            id_token_creds = google.auth.impersonated_credentials.IDTokenCredentials(
                target_credentials=impersonated_creds,
                target_audience=target_audience,
                include_email=True,
            )

            # Refresh to get the actual ID token
            auth_request_transport = Request()  # Use google.auth.transport.requests.Request
            id_token_creds.refresh(auth_request_transport)

            if not id_token_creds.token:
                raise ValueError("Failed to obtain ID token via impersonation (token is None).")

            logger.debug("Successfully generated ID token via impersonation.")
            return id_token_creds.token

        except Exception as e:
            logger.error(
                f"Failed to generate ID token via impersonation for "
                f"target_principal='{target_principal_email}', audience='{target_audience}'. Error: {e}"
            )
            raise  # Re-raise the exception to be handled by the caller

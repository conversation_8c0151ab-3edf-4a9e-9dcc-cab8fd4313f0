from typing import List, Dict, Any
from sqlalchemy import delete, select
from fastapi import APIRouter, Depends, HTTPException
from sqlalchemy.ext.asyncio import AsyncSession
import logging

from endpoints.models.schemas import CustomDataResponse
from endpoints.models.stories import Story
from endpoints.models.user_agreement_acceptance import UserAgreementAcceptance
from endpoints.models.users import User
from endpoints.util.api_responses_util import standard_responses
from endpoints.util.database import DatabaseSessionManager
from fastapi import Query, HTTPException, status
from sqlalchemy.exc import SQLAlchemyError
from fastapi.responses import JSONResponse

# Router initialization

logger = logging.getLogger(__name__)
remove_user_router = APIRouter(prefix="/user", tags=["User"])


@remove_user_router.delete(
    "/",
    response_model=CustomDataResponse[None],
    responses=standard_responses(
        error_404_description="User not found",
        error_500_description="Database error occurred",
        error_422_description="Invalid user email format",
    ),
)
async def delete_users(
    user_emails: str = Query(..., description="Comma-separated list of user emails"),
    db: AsyncSession = Depends(DatabaseSessionManager.get_session),
):
    user_emails = [email.strip() for email in user_emails.split(",") if email.strip()]


    try:
        # Fetch users matching valid emails
        result = await db.execute(
            select(User).where(User.auth_email.in_(user_emails))
        )
        users = result.scalars().all()

        if not users:
            return JSONResponse(
                status_code=status.HTTP_404_NOT_FOUND,
                content={"detail": "No users found for valid emails."}
            )

        found_emails = [user.auth_email for user in users]
        user_ids = [user.user_id for user in users]

        # Delete associated stories
        await db.execute(delete(Story).where(Story.user_id.in_(user_ids)))
        # Delete terms and conditions acceptance records
        await db.execute(delete(UserAgreementAcceptance).where(UserAgreementAcceptance.user_id.in_(user_ids)))
        # Delete users
        await db.execute(delete(User).where(User.user_id.in_(user_ids)))
        await db.commit()

        return CustomDataResponse(
            status_code=status.HTTP_200_OK,
            detail="All Users were deleted.",
        )

    except SQLAlchemyError as e:
        await db.rollback()
        logger.error(f"❌ Database error while deleting users: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Database error occurred while deleting users"
        )

from sqlalchemy import Column, Foreign<PERSON>ey, Boolean
from sqlalchemy.dialects.postgresql import UUID

from endpoints.models.base import Base


class Job(Base):
    __tablename__ = "jobs"

    user_id = Column(
        UUID(as_uuid=True),
        ForeignKey("users.user_id", ondelete="CASCADE"),
        primary_key=True,
    )
    subscription_id = Column(
        UUID(as_uuid=True),
        ForeignKey(
            "sales_smartstory_subscriptions.subscription_id", ondelete="CASCADE"
        ),
        primary_key=True,
    )

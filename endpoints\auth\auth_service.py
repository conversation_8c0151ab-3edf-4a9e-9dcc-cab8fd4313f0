################################################################################
#  JWT Manager Class
################################################################################
# file: auth_service.py
from typing import Optional, Dict


import os
from uuid import UUID

import jwt
import datetime
from fastapi import (
    Request,
    HTTPException,
    status,
)
from fastapi.security import OAuth2PasswordBearer
import logging

from endpoints.core.config import AppConfig

logger = logging.getLogger(__name__)

class AuthService:

    def __init__(
        self,
        secret: str = os.getenv("JWT_SECRET"),
        algorithm: str = "HS256",
        access_expires_minutes: int = 60,
        refresh_expires_minutes: int = 60 * 24,
    ):
        self.secret = secret
        if not self.secret:
            raise ValueError(
                "JWT_SECRET must be set in environment or passed explicitly."
            )

        self.algorithm = algorithm
        self.access_expires_minutes = int(
            os.environ.get("JWT_ACCESS_EXPIRES_MIN", access_expires_minutes)
        )
        self.refresh_expires_minutes = int(
            os.environ.get("JWT_REFRESH_EXPIRES_MIN", refresh_expires_minutes)
        )

        # For FastAPI OAuth2 scheme (Bearer tokens)
        self.oauth2_scheme = OAuth2PasswordBearer(tokenUrl="/auth/login")

    def generate_access_token(self, user_info: dict) -> str:
        now = datetime.datetime.now(datetime.UTC)
        exp_time = now + datetime.timedelta(minutes=self.access_expires_minutes)
        payload = {
            "sub": user_info["uid"],
            "name": user_info.get("name"),
            "provider": user_info.get("provider"),
            "iat": now,
            "exp": exp_time,
            "type": "access",
        }
        token = jwt.encode(payload, self.secret, algorithm=self.algorithm)
        return token if isinstance(token, str) else token.decode("utf-8")

    def generate_refresh_token(self, user_info: dict) -> str:
        now = datetime.datetime.utcnow()
        exp_time = now + datetime.timedelta(minutes=self.refresh_expires_minutes)
        payload = {
            "sub": user_info["uid"],
            "iat": now,
            "exp": exp_time,
            "type": "refresh",
        }
        token = jwt.encode(payload, self.secret, algorithm=self.algorithm)
        return token if isinstance(token, str) else token.decode("utf-8")

    def verify_token(self, token: str, allow_refresh: bool = False) -> dict:
        try:
            payload = jwt.decode(token, self.secret, algorithms=[self.algorithm])
        except jwt.ExpiredSignatureError:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED, detail="Token has expired"
            )
        except jwt.InvalidTokenError:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED, detail="Invalid token"
            )

        # If this is a refresh token but not allowed, reject it
        if not allow_refresh and payload.get("type") == "refresh":
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Refresh token used in an access-only context.",
            )

        return payload

    def refresh_access_token(self, refresh_token: str) -> str:
        payload = self.verify_token(refresh_token, allow_refresh=True)
        user_info = {"uid": payload["sub"]}
        return self.generate_access_token(user_info)

    async def verify_access_token_dependency(self, request: Request) -> Dict:
        """
        FastAPI dependency that:
          1) Extracts the token from the Authorization header via self.oauth2_scheme(request)
          2) Verifies it's a valid, unexpired access token
        """
        auth_header = request.headers.get("Authorization")
        if not auth_header:
            logger.error(f"No Authorization header found for path: {request.url.path}")
            raise HTTPException(status_code=401, detail="Missing authentication token")

        try:
            token = auth_header.split(" ")[1] if len(auth_header.split(" ")) > 1 else auth_header
            logger.info(f"Validating token for path: {request.url.path}")
            logger.debug(f"Token: {token[:10]}...")

            # Your existing token validation logic
            token_data = self.verify_token(token)
            logger.info(f"Successfully authenticated user {token_data.get('sub')} for path: {request.url.path}")
            return token_data
        except Exception as e:
            logger.error(f"Token validation failed for path {request.url.path}: {str(e)}")
            raise HTTPException(status_code=401, detail="Invalid authentication token")

    async def get_validated_user_id_dependency(self, request: Request) -> UUID:
      """FastAPI dependency that extracts token, verifies it, and returns User ID (UUID).
        Raises HTTPException on failure.
        """
      token = await self.oauth2_scheme(request) # Extract token using the scheme
      payload = self.verify_token(token, allow_refresh=False) # Verify token
      user_id_str = payload.get("sub") # Get user ID string from 'sub' claim
      if not user_id_str:
          raise HTTPException(status_code=status.HTTP_401_UNAUTHORIZED, detail="Invalid token payload (missing user identifier)")
      try:
          return UUID(user_id_str) # Convert to UUID and return
      except ValueError:
          raise HTTPException(status_code=status.HTTP_401_UNAUTHORIZED, detail="Invalid user identifier format in token")

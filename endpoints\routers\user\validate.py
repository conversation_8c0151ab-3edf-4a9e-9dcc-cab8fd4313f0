from fastapi import APIRouter, Depends, HTTPException, status
from pydantic import ValidationError, EmailStr
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.future import select
from sqlalchemy.exc import SQLAlchemyError, IntegrityError


from endpoints.models.schemas import CustomDataResponse
from endpoints.util.api_responses_util import standard_responses
from endpoints.util.database import DatabaseSessionManager
from endpoints.models.users import User
import logging

validation_router = APIRouter(prefix="/validate", tags=["Validation"])

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


@validation_router.get(
    "/validate_user/{email}",
    response_model=CustomDataResponse[None],
    responses=standard_responses(
        error_400_description="Invalid or duplicate email provided.",
        error_500_description="An unexpected error occurred during email validation.",
        error_422_description="Invalid input provided.",
    ),
)
async def validate_user(
    email: str, db: AsyncSession = Depends(DatabaseSessionManager.get_session)
):
    """
    Goal is to restrict registration of multiple users with the same business email.
    Validates whether a given business email is already associated with an existing user in the database.
    It checks for the uniqueness of the provided email. If the email already exists, an HTTPException
    is raised with an appropriate message. Otherwise, it confirms that the email is valid. The function
    also ensures that any database integrity errors or unexpected exceptions are handled gracefully.

    :param email: The email address to be validated.
    :param db: The active database session required for executing queries.
    :return: A dictionary containing a success message indicating that the email is valid.
    :rtype: dict
    :raises HTTPException: If the email already exists, a database integrity error occurs, or
                            an unexpected error is encountered.
    """
    try:
        email = email.lower().strip()
        logger.info(f"Attempting to validate email: {email}")

        # Use select() for type safety and better SQL generation
        stmt = select(User).where(User.business_email == email)
        result = await db.execute(stmt)
        db_user = result.scalars().first()  # Get the first result or None

        if db_user:
            logger.info(f"Email: {email} already exists.")
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST, detail="Duplicate email"
            )
        else:
            logger.info(f"Email: {email} is valid.")
            response = CustomDataResponse(
                status_code=status.HTTP_200_OK, detail="Email is valid.", data=None
            )

            return response

    except IntegrityError as e:
        logger.error(f"❌❌❌Integrity error during email validation: {e}")
        await db.rollback()
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Data integrity error during email validation.",
        )
    except SQLAlchemyError as e:
        logger.error(f"❌❌❌Database error during email validation: {e}")
        await db.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Database error: {e}",
        )
    except TypeError as e:
        logger.error(f"❌❌❌Type Error during email validation: {e}")
        await db.rollback()
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST, detail=f"Invalid data type : {e}"
        )
    except ValidationError as e:
        logger.error(f"❌❌❌Validation Error: {e}")
        raise HTTPException(
            status_code=status.HTTP_422_UNPROCESSABLE_ENTITY,
            detail=f"Validation Error : {e}",
        )
    except ValueError as e:
        logger.error(f"❌❌❌Value Error during email validation: {e}")
        await db.rollback()
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST, detail=f"Invalid input data : {e}"
        )
    except Exception as e:
        logger.error(f"❌❌❌Unexpected error during email validation: {e}")
        await db.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"An unexpected error occurred: {e}",
        )


@validation_router.post("/add_credits/{email}")
async def add_credits(
    email: EmailStr, db: AsyncSession = Depends(DatabaseSessionManager.get_session)
):
    """
    Add credits to the user account based on the user email.
    """
    email = str(email).lower().strip()
    logger.info(f"Attempting to validate email: {email}")
    from sqlalchemy import update, func, select
    from endpoints.models.sales_smartstory_subscriptions import (
        SalesSmartStorySubscription,
    )

    user_id = select(User.auth_email).where(User.auth_email == email)
    if not user_id:
        return CustomDataResponse(
            status_code=status.HTTP_404_NOT_FOUND, detail="Invalid email"
        )

    stmt = (
        update(SalesSmartStorySubscription)
        .where(SalesSmartStorySubscription.user_id == user_id)
        .where(SalesSmartStorySubscription.active_subscription == True)
        .values(
            available_credit=SalesSmartStorySubscription.available_credit + 50,
            updated_at=func.now(),
        )
    )
    await db.execute(stmt)
    return CustomDataResponse(
        status_code=status.HTTP_200_OK, detail="Added credits for the user", data=None
    )

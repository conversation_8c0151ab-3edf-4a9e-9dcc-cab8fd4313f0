import uuid

from endpoints.models.base import Base


from sqlalchemy import Column, String, Text, Boolean, Date, DateTime, ForeignKey
from sqlalchemy.dialects.postgresql import UUID
from sqlalchemy.sql import func


class Promotions(Base):
    __tablename__ = "promotions"

    promotion_id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    promotion_name = Column(String(255), nullable=False)
    promotion_description = Column(Text)
    promotion_code = Column(String(255), nullable=False)
    promotion_start_date = Column(Date)
    promotion_end_date = Column(Date)
    promotion_status = Column(Boolean, default=False)
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now())

    def __repr__(self):
        return f"<Promotion(id={self.promotion_id}, name={self.promotion_name})>"


import logging
from typing import List

from fastapi import APIRouter, Depends, HTTPException, status
from pydantic import UUID4
from sqlalchemy.exc import SQLAlchemyError
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.future import select

from endpoints.util.api_responses_util import standard_responses
from endpoints.util.database import DatabaseSessionManager
from endpoints.models.schemas import CustomDataResponse, HomepagePreviousRuns
from endpoints.models.stories import Story

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Router initialization
prospect_router = APIRouter(prefix="/user", tags=["User"])

# Constants
# todo add this number to the config
PROSPECT_LIST_LIMIT = 1000


@prospect_router.get(
    "/prospect_list/{user_id}",
    response_model=CustomDataResponse[List[HomepagePreviousRuns]],
    responses=standard_responses(
        error_404_description="No prospects found",
        error_500_description="Database error occurred",
        error_422_description="Invalid input provided",
    ),
)
async def get_prospect_list(
    user_id: UUID4,
    db: AsyncSession = Depends(DatabaseSessionManager.get_session),
):
    """
    Retrieve a list of prospects for a user, ordered by creation date.

    Args:
        user_id: UUID of the user
        db: Database session

    Returns:
        CustomDataResponse containing list of prospects with basic details
    """
    try:
        logger.info(f"Fetching prospect list for user ID: {user_id}")

        # Query stories for the user
        stories_result = await db.execute(
            select(Story)
            .filter(Story.user_id == user_id)
            .order_by(Story.created_at.desc())
            .limit(PROSPECT_LIST_LIMIT)
        )
        stories = stories_result.scalars().all()

        if not stories:
            logger.info(f"No prospects found for user ID: {user_id}")
            return CustomDataResponse(
                status_code=status.HTTP_200_OK, detail="No prospects found", data=[]
            )

        # Convert stories to HomepagePreviousRuns format
        prospect_list = [
            HomepagePreviousRuns(
                run_id=story.run_id,
                status=story.status.value,
                name=(story.prospect_first_name + " " + story.prospect_last_name) or "",
                company_name=story.prospect_company or "",
                created_at=story.created_at,
            )
            for story in stories
        ]

        logger.info(f"✅✅✅ Successfully fetched {len(prospect_list)} prospects")
        return CustomDataResponse(
            status_code=status.HTTP_200_OK,
            detail="Prospect list retrieved successfully",
            data=prospect_list,
        )

    except SQLAlchemyError as e:
        logger.error(f"❌❌❌ Database error while fetching prospect list: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Database error occurred",
        )
    except Exception as e:
        logger.error(f"❌❌❌ Unexpected error while fetching prospect list: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"An unexpected error occurred: {str(e)}",
        )

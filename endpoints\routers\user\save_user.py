import logging
import os
from typing import Optional

from fastapi import APIRouter, Depends, HTTPException, status
from pydantic import BaseModel, EmailStr, ValidationError
from sqlalchemy import update
from sqlalchemy.exc import SQLAlchemyError, IntegrityError
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.future import select

from endpoints.auth.auth_service import AuthService
from endpoints.models.promotions import Promotions
from endpoints.models.promotion_redemptions import PromotionRedemptions
from endpoints.models.user_agreement_acceptance import UserAgreementAcceptance
from endpoints.routers.auth.login import get_auth_service
from endpoints.util.api_responses_util import standard_responses
from endpoints.util.database import DatabaseSessionManager
from endpoints.models.schemas import (
    UserCreate,
    CustomDataResponse,
    UserCreateResponse,
    AuthenticationTokenResponse,

)
from endpoints.models.users import User
from endpoints.util.response_structure_model import (
    ErrorResponse,
    ValidationErrorResponse,
    ValidationErrorDetail,
)

# -----------------------
# Configure logging
# -----------------------
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# -----------------------
# Router Initialization
# -----------------------
save_user_router = APIRouter(prefix="/user", tags=["User"])


# -----------------------
# Service Layer
# -----------------------
class UserService:
    """Handles all user-related database operations and logic."""

    @staticmethod
    def _build_usercreate_response(
        db_user: User, token: str, payload_model: UserCreate
    ) -> UserCreateResponse:
        """
        Build a UserCreateResponse from a User object plus the new access token.
        We also base the rest of the fields on the original payload_model.
        """
        user_dict = payload_model.__dict__.copy()
        user_dict["token"] = token
        user_dict["user_id"] = db_user.user_id
        user_dict["plan_id"] = db_user.plan_id
        user_dict["is_user_provisioned"] = db_user.is_user_provisioned
        user_dict["is_user_locked_in"] = db_user.is_user_locked_in
        return UserCreateResponse(**user_dict)

    @staticmethod
    async def get_user_by_auth_email(
        db: AsyncSession, auth_email: EmailStr
    ) -> User | None:
        """Return a user by auth_email or None if not found."""
        result = await db.execute(select(User).filter(User.auth_email == auth_email))
        return result.scalars().first()

    @staticmethod
    async def get_user_by_auth_email_and_firebase_uid(
        db: AsyncSession, auth_email: EmailStr, firebase_uid: str
    ) -> User | None:
        """Return a user if both auth_email and firebase_uid match, otherwise None."""
        result = await db.execute(
            select(User).filter(
                (User.auth_email == auth_email) & (User.firebase_uid == firebase_uid)
            )
        )
        return result.scalars().first()

    @staticmethod
    async def get_user_by_business_email(
        db: AsyncSession, business_email: EmailStr
    ) -> User | None:
        """Return a user by business_email or None if not found."""
        result = await db.execute(
            select(User).filter(User.business_email == business_email)
        )
        return result.scalars().first()

    @staticmethod
    async def create_user(
        db: AsyncSession,
        user_data: UserCreate,
    ) -> User:
        """Create and persist a new user to the database."""
        new_user = User(
            auth_provider=user_data.auth_provider,
            firebase_uid=user_data.firebase_uid,
            auth_email=user_data.auth_email,
            first_name=str(user_data.first_name).title(),
            last_name=str(user_data.last_name).title(),
            company_name=str(user_data.company_name).title(),
            business_email=user_data.business_email,
        )
        db.add(new_user)
        await db.commit()
        await db.refresh(new_user)
        return new_user

    @staticmethod
    def _build_auth_token_response(
        user_obj: User,
        token: str,
        firebase_uid: str | None = None,
    ) -> AuthenticationTokenResponse:
        """
        Build and return an AuthenticationTokenResponse with the user data
        plus the provided access token.
        """
        user_dict = user_obj.__dict__.copy()
        user_dict["token"] = token
        user_dict["plan_id"] = user_obj.plan_id
        user_dict["is_user_locked_in"] = user_obj.is_user_locked_in
        # Overwrite firebase_uid if given
        if firebase_uid is not None:
            user_dict["firebase_uid"] = firebase_uid

        return AuthenticationTokenResponse.model_validate(user_dict)

    @staticmethod
    async def capture_terms_and_conditions_acceptance(user_id, ip_address, device_type, user_agreement_accepted, db: AsyncSession):
        """
        Capture the acceptance of terms and conditions by a user.
        """
        acceptance_record = UserAgreementAcceptance(
            user_id=user_id,
            ip_address=ip_address,
            device_type=device_type,
            user_agreement_accepted=user_agreement_accepted,
        )
        logger.info("User agreement acceptance captured successfully.")
        db.add(acceptance_record)

        await db.execute(
            update(User)
            .where(User.user_id == user_id)
            .values(is_user_locked_in=user_agreement_accepted)
        )
        await db.commit()
        await db.refresh(acceptance_record)

    @staticmethod
    async def register_promotion_use(user_data: UserCreate, db: AsyncSession):
        if user_data.promo_code:
            logger.info(f"User registered using promo code: {user_data.promo_code}")
            # todo review if this is really needed - reconciliation job can do this afterwards
            # promo_result = await db.execute(
            #     select(Promotion).where(Promotion.promotion_code == promo_code)
            # )
            # promo = promo_result.scalars().first()


            # Register the promo code usage regardless of promo validity or take extra actions if needed
            user_promo = PromotionRedemptions(
                user_email =user_data.auth_email,
                promo_code_used=user_data.promo_code,
            )
            db.add(user_promo)

# -----------------------
# Routes
# -----------------------


@save_user_router.get(
    "/{auth_email}",
    response_model=CustomDataResponse[AuthenticationTokenResponse],
    responses=standard_responses(
        error_404_description="User not found",
        error_500_description="Database error occurred / Unexpected Error Occurred",
        error_422_description="Validation Error At Server",
    ),
)
async def get_user_profile(
    frontend_uid: str,
    auth_email: EmailStr,
    db: AsyncSession = Depends(DatabaseSessionManager.get_session),
    auth_service: AuthService = Depends(get_auth_service),
):
    """
    Retrieve or create a user profile by `auth_email` and generate an auth token.
    If no user is found, a new user is created using the provided `frontend_uid`.
    Returns a JSON response with user data and the generated access token.
    """
    try:

        # Check if user exists
        user = await UserService.get_user_by_auth_email(db, auth_email)
        # If user does not exist, create one
        if not user:
            logger.info("No user found. Creating a new user ...")
            new_user = User(
                auth_email=auth_email,
                firebase_uid=frontend_uid,
                auth_provider="google",  # or "microsoft" or anything else
            )
            db.add(new_user)
            await db.commit()
            await db.refresh(new_user)
            user = new_user
            logger.info(f"New user created with email {auth_email}")
        else:
            logger.info(f"User found with email {auth_email}")

        # Generate the token
        token_payload = {
            "uid": str(user.user_id),
            "name": str(user.first_name or ""),
            "provider": str(user.auth_provider),
            "email": user.auth_email,
        }
        access_token = auth_service.generate_access_token(token_payload)

        # Build response data
        profile = UserService._build_auth_token_response(
            user_obj=user,
            token=access_token,
            firebase_uid=frontend_uid,
        )

        return CustomDataResponse(
            status_code=status.HTTP_200_OK,
            detail="User found (or created) successfully.",
            data=profile,
        )

    except SQLAlchemyError as e:
        logger.error(f"Database error: {e}")
        raise HTTPException(
            status_code=status.HTTP_409_CONFLICT,
            detail=f"Database error occurred: {e}",
        )
    except ValidationError as e:
        logger.error(f"Validation error: {e}")
        raise HTTPException(
            status_code=status.HTTP_422_UNPROCESSABLE_ENTITY,
            detail=f"Validation Error: {e}",
        )
    except ValueError as e:
        logger.error(f"Value error: {e}")
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"Value Error: {e}",
        )
    except Exception as e:
        logger.error(f"❌❌❌ Unexpected error: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"An unexpected error occurred: {e}",
        )



@save_user_router.post(
    "/save_user/",
    response_model=CustomDataResponse[UserCreateResponse],
    responses=standard_responses(
        error_400_description="Invalid data input or missing required field.",
        error_409_description="A user with this Duplicate identifier Found.",
        error_500_description="An unexpected error occurred while processing your request.",
        error_422_description="Validation Error At Server",
    ),
)
async def save_user(
    user_data: UserCreate,
    db: AsyncSession = Depends(DatabaseSessionManager.get_session),
    auth_service: AuthService = Depends(get_auth_service),
):
    """
    1. Check if a user exists with the same (auth_email, firebase_uid).
       - If found, update that user, commit, and return a token.
    2. Else, check if a user exists with the same business_email.
       - If found, return a token and code 202 ACCEPTED (no data updated).
    3. Otherwise, create a new user. Return a token and code 200 OK.
    """
    try:
        logger.info(
            f"Attempting to save or update user with auth_email={user_data.auth_email}, firebase_uid={user_data.firebase_uid}"
        )

        # 1. Check if user with same (auth_email, firebase_uid) exists
        existing_user = await UserService.get_user_by_auth_email_and_firebase_uid(
            db, user_data.auth_email, user_data.firebase_uid
        )

        if existing_user:
            logger.info(
                f"Found existing user with auth_email={user_data.auth_email} and "
                f"firebase_uid={user_data.firebase_uid}. Updating user..."
            )
            # Update existing user with new fields
            existing_user.auth_provider = user_data.auth_provider
            existing_user.business_email = user_data.business_email
            existing_user.company_name = user_data.company_name
            existing_user.first_name = user_data.first_name
            existing_user.last_name = user_data.last_name
            # todo add logic to not condition
            if user_data.user_agreement_accepted:
                await UserService.capture_terms_and_conditions_acceptance(
                    existing_user.user_id,
                    user_data.ip_address,
                    user_data.device_type,
                    user_data.user_agreement_accepted, db
                )

                await UserService.register_promotion_use(user_data, db)
                logger.info("User agreement acceptance and promotion use captured successfully.")


                # await db.execute(
                #     update(User)
                #     .where(User.user_id == existing_user.user_id)
                #     .values(is_user_locked_in=True)
                # )
                # logger.info("User locked in status updated successfully.")

            await db.commit()
            await db.refresh(existing_user)

            # Generate token
            payload = {
                "uid": str(existing_user.user_id),
                "email": existing_user.auth_email,
            }
            access_token = auth_service.generate_access_token(payload)
            logger.info("User token generated for updated user.")

            # Build response
            user_response = UserService._build_usercreate_response(
                db_user=existing_user, token=access_token, payload_model=user_data
            )
            return CustomDataResponse(
                status_code=status.HTTP_200_OK,
                detail="User updated successfully.",
                data=user_response,
            )

        # 2. If no match by (auth_email, firebase_uid), check business_email
        # existing_user_by_business = await UserService.get_user_by_business_email(
        #     db, user_data.business_email
        # )
        # if existing_user_by_business:
        #     logger.warning(
        #         f"User found by business_email={user_data.business_email}. Returning 202 ACCEPTED."
        #     )
        #     # Return partial info with token
        #     payload = {
        #         "uid": str(existing_user_by_business.user_id),
        #         "email": existing_user_by_business.auth_email,
        #     }
        #     access_token = auth_service.generate_access_token(payload)
        #     user_response = UserService._build_usercreate_response(
        #         db_user=existing_user_by_business,
        #         token=access_token,
        #         payload_model=user_data,
        #     )
        #     return CustomDataResponse(
        #         status_code=status.HTTP_202_ACCEPTED,
        #         detail=f"User {existing_user_by_business.auth_email} already exists via business_email.",
        #         data=user_response,
        #     )

        # 3. Otherwise, create new user
        logger.info("No duplicate found; creating new user...")
        db_user = await UserService.create_user(db, user_data)

        # Generate token
        payload = {"uid": str(db_user.user_id), "email": db_user.auth_email}
        access_token = auth_service.generate_access_token(payload)
        logger.info("User token generated for new user.")

        # Build final response
        user_response = UserService._build_usercreate_response(
            db_user=db_user, token=access_token, payload_model=user_data
        )

        await UserService.register_promotion_use(user_data, db)

        return CustomDataResponse(
            status_code=status.HTTP_200_OK,
            detail=f"User {db_user.auth_email} saved successfully.",
            data=user_response,
        )

    except IntegrityError as e:
        logger.error(f"Integrity error: {e}")
        await db.rollback()
        error_detail = str(e).lower()

        if "unique constraint" in error_detail:
            if "auth_email" in error_detail:
                detail_msg = (
                    "A user with this auth_email already exists. "
                    "Try a different email or log in."
                )
            elif "auth_token" in error_detail:
                detail_msg = (
                    "A user with this authentication token already exists. "
                    "Please use a different token."
                )
            elif "business_email" in error_detail:
                detail_msg = (
                    "A user with this business email already exists. "
                    "Please use a different business email."
                )
            else:
                detail_msg = (
                    "This user already exists in our system. Please log in instead."
                )
            return CustomDataResponse(
                status_code=status.HTTP_200_OK,
                detail=detail_msg,
                data=None,
            )
        elif "foreign key constraint" in error_detail:
            return CustomDataResponse(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Invalid reference to a related entity. Please check your input.",
                data=None,
            )
        elif "not-null constraint" in error_detail:
            return CustomDataResponse(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Missing required field. Please provide all required information.",
                data=None,
            )
        else:
            return CustomDataResponse(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"Data validation error: {str(e)}",
                data=None,
            )

    except ValidationError as e:
        logger.error(f"Validation error: {e}")
        raise HTTPException(
            status_code=status.HTTP_422_UNPROCESSABLE_ENTITY,
            detail=f"Invalid input: {e}",
        )
    except ValueError as e:
        logger.error(f"Value error: {e}")
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"Invalid input: {e}",
        )
    except SQLAlchemyError as e:
        logger.error(f"Database error: {e}")
        await db.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Database error occurred: {str(e)}",
        )
    except Exception as e:
        logger.error(f"Unexpected error: {e}")
        await db.rollback()
        raise
        # raise HTTPException(
        #     status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
        #     detail=f"An unexpected error occurred: {str(e)}",
        # )


# todo make this protected, shouldn't be exposed to front end - only admin
# todo add auth to this with a very secret special token
# get user profile
# @save_user_router.get(
#     "/get_users/",
#     responses=standard_responses(
#         error_500_description="Database error occurred / Unexpected Error Occurred",
#         error_422_description="Validation Error At Server",
#     ),
# )
# async def get_users(db: AsyncSession = Depends(DatabaseSessionManager.get_session)):
#     """
#     Retrieves all users from the database and returns them in a structured response.
#
#     This function interacts with the database to fetch all the available users using
#     SQLAlchemy's `select` statement and returns the result. It includes error handling
#     for both SQLAlchemy-specific errors and general exceptions, ensuring clear logging
#     of issues and appropriate HTTP responses in case of failures.
#
#     :param db: An asynchronous database session provided by dependency injection.
#     :type db: AsyncSession
#     :return: A structured JSON response containing a status code, a detail message,
#         and the list of retrieved users. In case of errors, returns an HTTPException
#         with an appropriate status code and error detail.
#     """
#     try:
#         result = await db.execute(select(User))
#         users = result.scalars().all()
#         response = CustomDataResponse(
#             status_code=status.HTTP_200_OK,
#             detail="Retrieved Users successfully",
#             data=users,
#         )
#         return response
#     except ValueError as e:
#         logger.error(f"❌❌❌ ValueError: {e}")
#         return HTTPException(
#             status_code=status.HTTP_422_UNPROCESSABLE_ENTITY,
#             detail=f"Invalid input: {e}",
#         )
#     except SQLAlchemyError as e:
#         logger.error(f"❌❌❌ Database error: {e}")
#         return HTTPException(
#             status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
#             detail=f"Internal Server Error: {e}",
#         )
#     except Exception as e:
#         logger.error(f"❌❌❌ Unexpected error: {e}")
#         return HTTPException(
#             status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
#             detail=f"An unexpected error occurred: {e}",
#         )
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Payment Cancelled</title>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap" rel="stylesheet">
    <style>
        :root {
            /* Core colors */
            --color-primary: #4361ee;
            --color-secondary: #3a0ca3;
            --color-power: #4895ef;
            --color-growth: #3a0ca3;
            --color-enterprise: #560bad;
            --color-success: #10b981;
            --color-error: #ef4444;
            --color-light: #f8f9fa;
            --color-dark: #212529;
            
            /* UI elements */
            --border-radius-sm: 6px;
            --border-radius: 8px;
            --border-radius-lg: 12px;
            --border-radius-xl: 20px;
            --spacing: 1rem;
            
            /* Shadows */
            --shadow-sm: 0 2px 4px rgba(0, 0, 0, 0.08);
            --shadow-md: 0 4px 8px rgba(0, 0, 0, 0.12);
            --shadow-lg: 0 8px 16px rgba(0, 0, 0, 0.15);
            --shadow-xl: 0 12px 24px rgba(0, 0, 0, 0.18);
            --shadow-inner: inset 0 2px 4px rgba(0, 0, 0, 0.06);
            
            /* Gradients */
            --gradient-primary: linear-gradient(135deg, #4361ee, #3a0ca3);
            --gradient-error: linear-gradient(135deg, #ef4444, #b91c1c);
            --gradient-card: linear-gradient(180deg, #ffffff, #f8f9fc);
            --gradient-enterprise: linear-gradient(135deg, #560bad, #7209b7);
            
            /* Transitions */
            --transition-fast: 0.2s ease;
            --transition-medium: 0.3s ease;
            --transition-slow: 0.5s ease;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', sans-serif;
            line-height: 1.6;
            color: var(--color-dark);
            background: linear-gradient(150deg, #f0f3ff 0%, #f9fafd 100%);
            padding: 0;
            margin: 0;
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .card {
            background: var(--gradient-card);
            border-radius: var(--border-radius-lg);
            overflow: hidden;
            width: 100%;
            max-width: 500px;
            position: relative;
            box-shadow: 
                var(--shadow-lg),
                0 0 0 1px rgba(67, 97, 238, 0.05),
                0 20px 40px rgba(67, 97, 238, 0.08);
            animation: slideUp 0.5s ease-out;
        }

        @keyframes slideUp {
            from {
                transform: translateY(30px);
                opacity: 0;
            }
            to {
                transform: translateY(0);
                opacity: 1;
            }
        }

        .card.error::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: var(--gradient-error);
            z-index: 1;
        }

        .card-header {
            padding: 2.5rem 2rem 1.5rem;
            text-align: center;
            position: relative;
        }

        .icon-container {
            width: 72px;
            height: 72px;
            background: var(--gradient-error);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 1.5rem;
            box-shadow: 
                var(--shadow-md),
                0 0 0 8px rgba(239, 68, 68, 0.1);
            animation: scaleIn 0.5s ease-out 0.5s both;
        }

        @keyframes scaleIn {
            from {
                transform: scale(0);
            }
            to {
                transform: scale(1);
            }
        }

        .icon-container svg {
            width: 40px;
            height: 40px;
            fill: white;
        }

        .card-title {
            font-size: 1.75rem;
            font-weight: 700;
            margin-bottom: 0.75rem;
            color: var(--color-dark);
            letter-spacing: -0.02em;
        }

        .card-description {
            color: #6c757d;
            font-size: 1.1rem;
            max-width: 380px;
            margin: 0 auto;
        }

        .card-content {
            padding: 1.5rem 2rem 2rem;
        }

        .button-container {
            display: flex;
            gap: 1.25rem;
            justify-content: center;
            margin-bottom: 2rem;
            flex-wrap: wrap;
        }

        .button {
            position: relative;
            padding: 0.85rem 1.5rem;
            border-radius: var(--border-radius);
            font-size: 0.95rem;
            cursor: pointer;
            transition: 
                transform var(--transition-fast), 
                box-shadow var(--transition-fast),
                background-position var(--transition-medium);
            text-align: center;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            min-width: 180px;
            letter-spacing: 0.2px;
            overflow: hidden;
        }

        .button.primary {
            background: var(--gradient-primary);
            background-size: 200% 200%;
            background-position: 0% 0%;
            color: white;
            font-weight: 600;
            box-shadow: 
                var(--shadow-md),
                0 0 0 1px rgba(67, 97, 238, 0.1),
                0 10px 15px -5px rgba(67, 97, 238, 0.2);
            transition: 
                transform var(--transition-fast), 
                box-shadow var(--transition-fast),
                background-position var(--transition-medium),
                border-color var(--transition-medium);
        }
        
        .button.primary:hover {
            box-shadow: 
                var(--shadow-lg),
                0 0 0 1px rgba(67, 97, 238, 0.15),
                0 15px 20px -5px rgba(67, 97, 238, 0.25);
        }
        
        .button.secondary {
            background: white;
            color: var(--color-dark);
            border: 1px solid #dee2e6;
            font-weight: 500;
            box-shadow: 
                var(--shadow-sm),
                0 4px 6px rgba(0, 0, 0, 0.06);
            transition: 
                transform var(--transition-fast), 
                box-shadow var(--transition-fast),
                background-color var(--transition-medium),
                border-color var(--transition-medium);
        }
        
        .button.secondary:hover {
            background-color: #f8f9fa;
            border-color: #c1c9d1;
            box-shadow: 
                var(--shadow-md),
                0 6px 12px rgba(0, 0, 0, 0.08);
        }
        
        .button.primary:before,
        .button.secondary:before {
            content: "";
            display: inline-block;
            width: 18px;
            height: 18px;
            margin-right: 8px;
            background-repeat: no-repeat;
            background-position: center;
            background-size: contain;
            vertical-align: text-bottom;
        }
        
        .button.primary:before {
            background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='white'%3E%3Cpath d='M17.65 6.35C16.2 4.9 14.21 4 12 4c-4.42 0-7.99 3.58-7.99 8s3.57 8 7.99 8c3.73 0 6.84-2.55 7.73-6h-2.08c-.82 2.33-3.04 4-5.65 4-3.31 0-6-2.69-6-6s2.69-6 6-6c1.66 0 3.14.69 4.22 1.78L13 11h7V4l-2.35 2.35z'/%3E%3C/svg%3E");
        }
        
        .button.secondary:before {
            background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%23212529'%3E%3Cpath d='M10 20v-6h4v6h5v-8h3L12 3 2 12h3v8z'/%3E%3C/svg%3E");
        }

        .help-section {
            background-color: #f8f9fc;
            border-radius: var(--border-radius);
            padding: 1.25rem;
            text-align: center;
            border: 1px solid rgba(67, 97, 238, 0.1);
        }

        .help-title {
            font-weight: 600;
            margin-bottom: 0.75rem;
            color: var(--color-dark);
        }

        .help-link {
            background: var(--gradient-enterprise);
            background-size: 200% 200%;
            background-position: 0% 0%;
            color: white;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            gap: 0.5rem;
            padding: 0.75rem 1.25rem;
            border-radius: var(--border-radius);
            text-decoration: none;
            font-weight: 600;
            transition: 
                transform var(--transition-fast), 
                box-shadow var(--transition-fast),
                background-position var(--transition-medium);
            box-shadow: 
                var(--shadow-md),
                0 0 0 1px rgba(86, 11, 173, 0.1),
                0 10px 15px -5px rgba(86, 11, 173, 0.2);
        }

        .help-link:hover {
            transform: translateY(-2px);
            box-shadow: 
                var(--shadow-lg),
                0 0 0 1px rgba(86, 11, 173, 0.15),
                0 15px 20px -5px rgba(86, 11, 173, 0.25);
            background-position: 100% 100%;
        }

        .help-link svg {
            width: 20px;
            height: 20px;
        }

        @media (max-width: 576px) {
            .card {
                margin: 1rem;
                max-width: none;
            }
            
            .card-header,
            .card-content {
                padding-left: 1.5rem;
                padding-right: 1.5rem;
            }
            
            .card-title {
                font-size: 1.5rem;
            }
            
            .card-description {
                font-size: 1rem;
            }
            
            .button-container {
                flex-direction: column;
            }
            
            .button {
                width: 100%;
            }
        }

        /* Simple hover effect for the primary and secondary buttons */
        .button.primary:hover,
        .button.secondary:hover {
            transform: translateY(-2px);
        }

        .logo-link {
            position: absolute;
            top: 20px;
            left: 60px;
            z-index: 10;
        }

        .top-logo {
            height: 40px;
            width: auto;
            display: block;
        }

        @media (max-width: 768px) {
            .top-logo {
                height: 30px;
            }
        }

        /* For pages with back button */
        .card-back-button {
            position: absolute;
            top: 20px;
            left: 20px;
            z-index: 10;
        }
    </style>
</head>
<body>
    <a href="{{ frontend_url }}/home" class="logo-link">
        <img src="{{ url_for('static', path='logo_pl.png') }}" alt="Prospect Intelligence Logo" class="top-logo">
    </a>
    
    <div class="container">
        <div class="card error">
            <div class="card-header">
                <div class="icon-container">
                    <svg viewBox="0 0 24 24">
                        <path d="M19 6.41L17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12z"/>
                    </svg>
                </div>
                <h1 class="card-title">Payment Canceled</h1>
                <p class="card-description">Your payment process was cancelled. No charges have been made to your account. Would you like to try again or need help?</p>
            </div>
            
            <div class="card-content">
                <div class="button-container">
                    {% if user_id %}
                        <a href="{{ frontend_url }}/pricing" class="button primary">Try Again</a>
                    {% else %}
                    {% endif %}
                    <a href="{{ frontend_url }}/home" class="button secondary">Return to Home</a>
                </div>
                
                <div class="help-section">
                    <p class="help-title">Having trouble with your payment?</p>
                    <a href="mailto:{{ support_email if support_email else '<EMAIL>' }}" class="help-link">
                        <svg viewBox="0 0 24 24" fill="currentColor">
                            <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm1 17h-2v-2h2v2zm2.07-7.75l-.9.92C13.45 12.9 13 13.5 13 15h-2v-.5c0-1.1.45-2.1 1.17-2.83l1.24-1.26c.37-.36.59-.86.59-1.41 0-1.1-.9-2-2-2s-2 .9-2 2H8c0-2.21 1.79-4 4-4s4 1.79 4 4c0 .88-.36 1.68-.93 2.25z"/>
                        </svg>
                        Contact Support
                    </a>
                </div>
            </div>
        </div>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Add gradient animation to ALL buttons, including the Try Again and Return to Dashboard buttons
            const allButtons = document.querySelectorAll('.button, .help-link');
            
            document.addEventListener('mousemove', function(e) {
                const mouseX = e.clientX / window.innerWidth;
                const mouseY = e.clientY / window.innerHeight;
                
                allButtons.forEach(button => {
                    const rect = button.getBoundingClientRect();
                    const btnCenterX = rect.left + rect.width / 2;
                    const btnCenterY = rect.top + rect.height / 2;
                    
                    const distanceX = (e.clientX - btnCenterX) / (window.innerWidth / 2) * 5;
                    const distanceY = (e.clientY - btnCenterY) / (window.innerHeight / 2) * 5;
                    
                    if (Math.abs(distanceX) < 20 && Math.abs(distanceY) < 20) {
                        // Different handling for gradient vs non-gradient buttons
                        if (button.classList.contains('secondary')) {
                            // For secondary button, apply a subtle highlight effect
                            const intensity = Math.max(0, 1 - (Math.abs(distanceX) + Math.abs(distanceY)) / 25);
                            button.style.boxShadow = `var(--shadow-md), 0 4px 12px rgba(67, 97, 238, ${intensity * 0.12})`;
                            button.style.borderColor = `rgba(${67 + (intensity * 20)}, ${97 + (intensity * 10)}, ${238 - (intensity * 30)}, ${0.2 + (intensity * 0.2)})`;
                        } else {
                            // For gradient buttons, animate the gradient position
                            button.style.backgroundPosition = `${50 + distanceX}% ${50 + distanceY}%`;
                        }

                    }

                });
            });
            
            // Reset styles when mouse leaves the button
            allButtons.forEach(button => {
                button.addEventListener('mouseleave', function() {
                    if (button.classList.contains('secondary')) {
                        button.style.boxShadow = '';
                        button.style.borderColor = '';
                    } else {
                        button.style.backgroundPosition = '0% 0%';
                    }
                });
            });
        });
    </script>
</body>
</html>
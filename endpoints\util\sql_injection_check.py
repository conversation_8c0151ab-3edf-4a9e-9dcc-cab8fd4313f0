import re
from typing import Any
from functools import wraps
from fastapi import HTTPException
from pydantic import EmailStr


def check_sql_injection(input_data: EmailStr) -> bool:
    """
    Performs detection of potential SQL injection patterns in the provided input data.

    This function uses a series of regular expressions to identify potential SQL injection
    attempts in a given input string. The patterns include common SQL keywords, malicious
    sequences such as comments, semicolons, OR/AND statements with true conditions, and
    database metadata access methods. If any of these patterns are detected in the input,
    it flags the input as potentially malicious.

    Parameters:
        input_data: str
            The string input to be analyzed for potential SQL injection patterns.

    Returns:
        bool
            Returns True if a potential SQL injection pattern is detected; otherwise,
            returns False.

    Raises:
        None
    """
    sql_patterns = [
        r"(?i)\b(select|insert|update|delete|drop|alter|create|exec|union|where|truncate|column|table)\b",
        r"(--|#|/\*|\*/|;|'|--\s)",  # SQL comments, semicolon, single-quote
        r"(\bOR\b\s+\d+=\d+|\bAND\b\s+\d+=\d+)",  # OR 1=1, AND 1=1 attacks
        r"(\bUNION\b\s+\bSELECT\b)",  # UNION-based attacks
        r"(@@version|@@hostname|database\(\)|information_schema)",  # Database metadata extraction
    ]

    for pattern in sql_patterns:
        if re.search(pattern, input_data):
            print(f"Potential SQL injection detected in input: {input_data}")
            return True
    return False


def check_sql_injection_decorator(func):
    @wraps(func)
    async def wrapper(*args, **kwargs):
        # Extract the request body from kwargs (FastAPI passes data as Pydantic models)
        for key, value in kwargs.items():
            if isinstance(value, EmailStr) and check_sql_injection(value):
                raise HTTPException(
                    status_code=400, detail=f"Invalid input detected in field: {key}"
                )
            elif isinstance(
                value, dict
            ):  # If input is a dictionary (e.g., Pydantic model)
                for field, field_value in value.items():
                    if isinstance(field_value, EmailStr) and check_sql_injection(
                        field_value
                    ):
                        raise HTTPException(
                            status_code=400,
                            detail=f"Invalid input detected in field: {field}",
                        )

        return await func(*args, **kwargs)

    return wrapper

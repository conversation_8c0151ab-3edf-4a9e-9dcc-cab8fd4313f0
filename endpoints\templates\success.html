<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Payment Successful</title>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap" rel="stylesheet">
    <style>
        :root {
            /* Core colors */
            --color-primary: #4361ee;
            --color-secondary: #3a0ca3;
            --color-power: #4895ef;
            --color-growth: #3a0ca3;
            --color-enterprise: #560bad;
            --color-success: #10b981;
            --color-error: #ef4444;
            --color-light: #f8f9fa;
            --color-dark: #212529;
            
            /* UI elements */
            --border-radius-sm: 6px;
            --border-radius: 8px;
            --border-radius-lg: 12px;
            --border-radius-xl: 20px;
            --spacing: 1rem;
            
            /* Shadows */
            --shadow-sm: 0 2px 4px rgba(0, 0, 0, 0.08);
            --shadow-md: 0 4px 8px rgba(0, 0, 0, 0.12);
            --shadow-lg: 0 8px 16px rgba(0, 0, 0, 0.15);
            --shadow-xl: 0 12px 24px rgba(0, 0, 0, 0.18);
            --shadow-inner: inset 0 2px 4px rgba(0, 0, 0, 0.06);
            
            /* Gradients */
            --gradient-primary: linear-gradient(135deg, #4361ee, #3a0ca3);
            --gradient-success: linear-gradient(135deg, #10b981, #059669);
            --gradient-card: linear-gradient(180deg, #ffffff, #f8f9fc);
            
            /* Transitions */
            --transition-fast: 0.2s ease;
            --transition-medium: 0.3s ease;
            --transition-slow: 0.5s ease;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', sans-serif;
            line-height: 1.6;
            color: var(--color-dark);
            background: linear-gradient(150deg, #f0f3ff 0%, #f9fafd 100%);
            padding: 0;
            margin: 0;
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .card {
            background: var(--gradient-card);
            border-radius: var(--border-radius-lg);
            overflow: hidden;
            width: 100%;
            max-width: 500px;
            position: relative;
            box-shadow: 
                var(--shadow-lg),
                0 0 0 1px rgba(67, 97, 238, 0.05),
                0 20px 40px rgba(67, 97, 238, 0.08);
            animation: slideUp 0.5s ease-out;
        }

        @keyframes slideUp {
            from {
                transform: translateY(30px);
                opacity: 0;
            }
            to {
                transform: translateY(0);
                opacity: 1;
            }
        }

        .card.success::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: var(--gradient-success);
            z-index: 1;
        }

        .card-header {
            padding: 2.5rem 2rem 1.5rem;
            text-align: center;
            position: relative;
        }

        .icon-container {
            width: 72px;
            height: 72px;
            background: var(--gradient-success);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 1.5rem;
            box-shadow: 
                var(--shadow-md),
                0 0 0 8px rgba(16, 185, 129, 0.1);
            animation: scaleIn 0.5s ease-out 0.5s both;
        }

        @keyframes scaleIn {
            from {
                transform: scale(0);
            }
            to {
                transform: scale(1);
            }
        }

        .icon-container svg {
            width: 40px;
            height: 40px;
            fill: white;
        }

        .card-title {
            font-size: 1.75rem;
            font-weight: 700;
            margin-bottom: 0.75rem;
            color: var(--color-dark);
            letter-spacing: -0.02em;
        }

        .card-description {
            color: #6c757d;
            font-size: 1.1rem;
            max-width: 380px;
            margin: 0 auto;
        }

        .card-content {
            padding: 1.5rem 2rem 2rem;
        }

        .details {
            background-color: #f8f9fc;
            border-radius: var(--border-radius);
            padding: 1.25rem;
            margin-bottom: 1.5rem;
            border: 1px solid rgba(67, 97, 238, 0.1);
        }

        .detail-row {
            display: flex;
            justify-content: space-between;
            padding: 0.75rem 0;
            border-bottom: 1px solid rgba(0, 0, 0, 0.05);
        }

        .detail-row:last-child {
            border-bottom: none;
        }

        .detail-label {
            font-weight: 500;
            color: #6c757d;
        }

        .detail-value {
            font-weight: 600;
            color: var(--color-dark);
        }

        .button-container {
            display: flex;
            gap: 1rem;
            justify-content: center;
        }

        .button {
            position: relative;
            background: var(--gradient-primary);
            background-size: 200% 200%;
            background-position: 0% 0%;
            color: white;
            border: none;
            padding: 0.85rem 1.5rem;
            border-radius: var(--border-radius);
            font-weight: 600;
            font-size: 0.95rem;
            cursor: pointer;
            transition: 
                transform var(--transition-fast), 
                box-shadow var(--transition-fast),
                background-position var(--transition-medium);
            text-align: center;
            text-decoration: none;
            display: inline-block;
            box-shadow: 
                var(--shadow-md),
                0 0 0 1px rgba(67, 97, 238, 0.1),
                0 10px 15px -5px rgba(67, 97, 238, 0.2);
            letter-spacing: 0.2px;
            overflow: hidden;
        }

        .button:hover {
            transform: translateY(-3px);
            box-shadow: 
                var(--shadow-lg),
                0 0 0 1px rgba(67, 97, 238, 0.15),
                0 15px 20px -5px rgba(67, 97, 238, 0.25);
            background-position: 100% 100%;
        }
        
        .button:active {
            transform: translateY(-1px);
            box-shadow: 
                var(--shadow-sm),
                0 0 0 1px rgba(67, 97, 238, 0.2);
        }
        
        .button.success {
            background: var(--gradient-success);
            background-size: 200% 200%;
            background-position: 0% 0%;
            box-shadow: 
                var(--shadow-md),
                0 0 0 1px rgba(16, 185, 129, 0.1),
                0 10px 15px -5px rgba(16, 185, 129, 0.2);
        }
        
        .button.success:hover {
            box-shadow: 
                var(--shadow-lg),
                0 0 0 1px rgba(16, 185, 129, 0.15),
                0 15px 20px -5px rgba(16, 185, 129, 0.25);
        }

        /* Create a primary-success button style that's green instead of blue */
        .button.primary-success {
            position: relative;
            background: var(--gradient-success);
            background-size: 200% 200%;
            background-position: 0% 0%;
            color: white;
            border: none;
            padding: 0.85rem 1.5rem;
            border-radius: var(--border-radius);
            font-weight: 600;
            font-size: 0.95rem;
            cursor: pointer;
            transition: 
                transform var(--transition-fast), 
                box-shadow var(--transition-fast),
                background-position var(--transition-medium);
            text-align: center;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            min-width: 200px;
            box-shadow: 
                var(--shadow-md),
                0 0 0 1px rgba(16, 185, 129, 0.1),
                0 10px 15px -5px rgba(16, 185, 129, 0.2);
            letter-spacing: 0.2px;
            overflow: hidden;
        }
        
        .button.primary-success:hover {
            transform: translateY(-3px);
            box-shadow: 
                var(--shadow-lg),
                0 0 0 1px rgba(16, 185, 129, 0.15),
                0 15px 20px -5px rgba(16, 185, 129, 0.25);
            background-position: 100% 100%;
        }
        
        .button.primary-success:active {
            transform: translateY(-1px);
            box-shadow: 
                var(--shadow-sm),
                0 0 0 1px rgba(16, 185, 129, 0.2);
        }
        
        /* Add dashboard icon to the button */
        .button.primary-success:before {
            content: "";
            display: inline-block;
            width: 18px;
            height: 18px;
            margin-right: 8px;
            background-repeat: no-repeat;
            background-position: center;
            background-size: contain;
            vertical-align: text-bottom;
            background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='white'%3E%3Cpath d='M10 20v-6h4v6h5v-8h3L12 3 2 12h3v8z'/%3E%3C/svg%3E");
        }

        @media (max-width: 576px) {
            .card {
                margin: 1rem;
                max-width: none;
            }
            
            .card-header,
            .card-content {
                padding-left: 1.5rem;
                padding-right: 1.5rem;
            }
            
            .card-title {
                font-size: 1.5rem;
            }
            
            .card-description {
                font-size: 1rem;
            }
        }

        .logo-link {
            position: absolute;
            top: 20px;
            left: 20px;
            z-index: 10;
        }

        .top-logo {
            height: 40px;
            width: auto;
            display: block;
        }

        @media (max-width: 768px) {
            .top-logo {
                height: 30px;
            }
        }
    </style>
</head>
<body>
    <a href="{{ frontend_url }}/home" class="logo-link">
        <img src="{{ url_for('static', path='logo_pl.png') }}" alt="Prospect Intelligence Logo" class="top-logo">
    </a>
    
    <div class="container">
        <div class="card success">
            <div class="card-header">
                <div class="icon-container">
                    <svg viewBox="0 0 24 24">
                        <path d="M9 16.17L4.83 12l-1.42 1.41L9 19 21 7l-1.41-1.41L9 16.17z"/>
                    </svg>
                </div>
                <h1 class="card-title">Payment Successful!</h1>
                <p class="card-description">Thank you for your subscription. We've sent you an email with all the details of your purchase.</p>
            </div>
            
            <div class="card-content">
                {% if processing %}
                    <div class="processing-status">
                        <p>Your payment is being processed. This can take a few moments.</p>
                        <div class="loader"></div>
                        <script>
                            // Auto-refresh the page every 5 seconds to check status
                            setTimeout(function() {
                                window.location.reload();
                            }, 5000);
                        </script>
                    </div>
                {% else %}
                    <div class="details">
                        <div class="detail-row">
                            <span class="detail-label">Order ID</span>
                            <span class="detail-value">{{ order_id if order_id else 'N/A' }}</span>
                        </div>
                        <div class="detail-row">
                            <span class="detail-label">Date</span>
                            <span class="detail-value">{{ date if date else now().strftime('%Y-%m-%d') }}</span>
                        </div>
                        <div class="detail-row">
                            <span class="detail-label">Plan</span>
                            <span class="detail-value">{{ plan if plan else 'Growth' }}</span>
                        </div>
                    </div>
                    
                    <div class="button-container">
                        <a href="{{ frontend_url }}" class="button primary-success">Go to Homepage</a>
                    </div>
                {% endif %}
            </div>
        </div>
        <script>
            setTimeout(function() {
                window.location.href = '{{ frontend_url }}';
            }, 5000);
        </script>
    </div>
</body>
</html>

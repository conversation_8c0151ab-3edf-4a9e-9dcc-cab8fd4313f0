<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="description" content="Manage your payment details and plan">
    <title>Account & Billing</title>
    <script src="https://js.stripe.com/v3/"></script>
    <script>
        const currentUserId = '{{user_id}}';
    </script>
    <script src="{{ url_for('static', path='script.js') }}" defer></script>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap" rel="stylesheet">
    <style>
        :root {
            /* Core colors */
            --color-primary: #4361ee;
            --color-secondary: #3a0ca3;
            --color-power: #4895ef;
            --color-growth: #3a0ca3;
            --color-enterprise: #560bad;
            --color-light: #f8f9fa;
            --color-dark: #212529;
            
            /* UI elements */
            --border-radius-sm: 6px;
            --border-radius: 8px;
            --border-radius-lg: 12px;
            --border-radius-xl: 20px;
            --spacing: 1rem;
            
            /* Shadows */
            --shadow-sm: 0 2px 4px rgba(0, 0, 0, 0.08);
            --shadow-md: 0 4px 8px rgba(0, 0, 0, 0.12);
            --shadow-lg: 0 8px 16px rgba(0, 0, 0, 0.15);
            --shadow-xl: 0 12px 24px rgba(0, 0, 0, 0.18);
            --shadow-inner: inset 0 2px 4px rgba(0, 0, 0, 0.06);
            
            /* Gradients */
            --gradient-primary: linear-gradient(135deg, #4361ee, #3a0ca3);
            --gradient-power: linear-gradient(135deg, #4895ef, #4361ee);
            --gradient-growth: linear-gradient(135deg, #3a0ca3, #560bad);
            --gradient-enterprise: linear-gradient(135deg, #560bad, #7209b7);
            --gradient-light: linear-gradient(135deg, rgba(255,255,255,0.9), rgba(255,255,255,0.6));
            --gradient-card: linear-gradient(180deg, #ffffff, #f8f9fc);
            
            /* Transitions */
            --transition-fast: 0.2s ease;
            --transition-medium: 0.3s ease;
            --transition-slow: 0.5s ease;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', sans-serif;
            line-height: 1.6;
            color: var(--color-dark);
            background: linear-gradient(150deg, #f0f3ff 0%, #f9fafd 100%);
            padding: 0;
            margin: 0;
            min-height: 100vh;
        }

        .container {
            max-width: 600px;
            margin: 0 auto;
            padding: 2rem;
        }

        .card {
            background: var(--gradient-card);
            border-radius: var(--border-radius-lg);
            overflow: hidden;
            margin-bottom: 2rem;
            position: relative;
            box-shadow: 
                var(--shadow-lg),
                0 0 0 1px rgba(67, 97, 238, 0.05),
                0 20px 40px rgba(67, 97, 238, 0.08);
            transition: none;
        }

        .card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: var(--gradient-primary);
            z-index: 1;
        }
        
        .card:hover {
            transform: none;
            box-shadow: 
                var(--shadow-lg),
                0 0 0 1px rgba(67, 97, 238, 0.05),
                0 20px 40px rgba(67, 97, 238, 0.08);
        }

        .header {
            padding: 2.5rem 2rem 2rem;
            position: relative;
            text-align: center;
            background: linear-gradient(to bottom, rgba(67, 97, 238, 0.03), rgba(255, 255, 255, 0));
            border-bottom: 1px solid rgba(67, 97, 238, 0.08);
        }

        .header h1 {
            font-size: 1.75rem;
            font-weight: 600;
            color: var(--color-dark);
            margin-bottom: 0.5rem;
            letter-spacing: -0.02em;
            background: var(--gradient-primary);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .header p {
            color: #6c757d;
            font-size: 1rem;
        }

        .content {
            padding: 2rem;
        }

        .account-info {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding-bottom: 1.5rem;
            margin-bottom: 1.5rem;
            position: relative;
        }
        
        .account-info::after {
            content: "";
            position: absolute;
            bottom: 0;
            left: 0;
            right: 0;
            height: 1px;
            background: linear-gradient(to right, 
                rgba(67, 97, 238, 0), 
                rgba(67, 97, 238, 0.15), 
                rgba(67, 97, 238, 0));
        }

        .account-identity {
            display: flex;
            align-items: center;
        }

        .account-avatar {
            width: 48px;
            height: 48px;
            border-radius: 50%;
            background: var(--gradient-primary);
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: 600;
            margin-right: 1rem;
            flex-shrink: 0;
            box-shadow: 
                var(--shadow-md),
                0 0 0 3px rgba(255, 255, 255, 0.6),
                0 0 0 4px rgba(67, 97, 238, 0.15);
            text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
        }

        .account-details {
            min-width: 0;
        }

        .account-details h3 {
            font-size: 1rem;
            margin-bottom: 0.25rem;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
            font-weight: 600;
            color: var(--color-dark);
        }

        .account-id {
            font-size: 0.875rem;
            color: #6c757d;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
        }

        .account-actions {
            margin-left: 1rem;
            z-index: 2;
        }

        .manage-button {
            position: relative;
            background: var(--gradient-enterprise);
            color: white;
            border: none;
            padding: 0.5rem 1rem;
            border-radius: var(--border-radius-xl);
            font-size: 0.8rem;
            font-weight: 600;
            letter-spacing: 0.3px;
            cursor: pointer;
            transition: 
                transform var(--transition-fast), 
                box-shadow var(--transition-fast),
                background-position var(--transition-medium);
            background-size: 200% 200%;
            background-position: 0% 0%;
            box-shadow: 
                var(--shadow-md),
                0 0 0 1px rgba(86, 11, 173, 0.1),
                0 10px 15px -5px rgba(86, 11, 173, 0.2);
            overflow: hidden;
            text-transform: uppercase;
        }

        .manage-button:hover {
            transform: translateY(-2px);
            box-shadow: 
                var(--shadow-lg),
                0 0 0 1px rgba(86, 11, 173, 0.15),
                0 15px 20px -5px rgba(86, 11, 173, 0.25);
            background-position: 100% 100%;
        }
        
        .manage-button:active {
            transform: translateY(0);
            box-shadow: 
                var(--shadow-sm),
                0 0 0 1px rgba(86, 11, 173, 0.2);
        }

        .current-plan {
            background: linear-gradient(135deg, rgba(67, 97, 238, 0.04), rgba(67, 97, 238, 0.01));
            border-radius: var(--border-radius);
            padding: 1.75rem;
            margin-bottom: 2rem;
            border: 1px solid rgba(67, 97, 238, 0.15);
            text-align: center;
            position: relative;
            overflow: hidden;
            box-shadow: 
                var(--shadow-sm),
                inset 0 1px 0 rgba(255, 255, 255, 0.6);
            transition: transform var(--transition-medium), box-shadow var(--transition-medium);
        }
        
        .current-plan:hover {
            transform: translateY(-2px);
            box-shadow: 
                var(--shadow-md),
                0 0 0 1px rgba(67, 97, 238, 0.2),
                inset 0 1px 0 rgba(255, 255, 255, 0.8);
        }

        .current-plan::before {
            content: "";
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: var(--gradient-primary);
            z-index: 1;
        }

        .plan-label {
            display: inline-block;
            background: var(--gradient-primary);
            color: white;
            font-size: 0.75rem;
            font-weight: 600;
            padding: 0.3rem 0.85rem;
            border-radius: var(--border-radius-xl);
            margin-bottom: 0.75rem;
            box-shadow: 
                var(--shadow-sm),
                0 0 0 1px rgba(67, 97, 238, 0.2),
                0 5px 10px -3px rgba(67, 97, 238, 0.25);
            letter-spacing: 0.5px;
            text-transform: uppercase;
        }

        .plan-name {
            font-weight: 700;
            font-size: 1.5rem;
            margin-bottom: 0.5rem;
            background: var(--gradient-primary);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            letter-spacing: -0.01em;
        }

        .button-container {
            display: flex;
            gap: 1rem;
            margin-top: 1.5rem;
            flex-direction: column;
        }

        .button {
            position: relative;
            background: var(--gradient-primary);
            background-size: 200% 200%;
            background-position: 0% 0%;
            color: white;
            border: none;
            padding: 0.85rem 1.5rem;
            border-radius: var(--border-radius);
            font-weight: 600;
            font-size: 0.95rem;
            cursor: pointer;
            transition: 
                transform var(--transition-fast), 
                box-shadow var(--transition-fast),
                background-position var(--transition-medium);
            text-align: center;
            width: 100%;
            box-shadow: 
                var(--shadow-md),
                0 0 0 1px rgba(67, 97, 238, 0.1),
                0 10px 15px -5px rgba(67, 97, 238, 0.2);
            letter-spacing: 0.2px;
            overflow: hidden;
        }

        .button:hover {
            transform: translateY(-3px);
            box-shadow: 
                var(--shadow-lg),
                0 0 0 1px rgba(67, 97, 238, 0.15),
                0 15px 20px -5px rgba(67, 97, 238, 0.25);
            background-position: 100% 100%;
        }
        
        .button:active {
            transform: translateY(-1px);
            box-shadow: 
                var(--shadow-sm),
                0 0 0 1px rgba(67, 97, 238, 0.2);
        }

        .button.power {
            background: var(--gradient-power);
            background-size: 200% 200%;
            background-position: 0% 0%;
            box-shadow: 
                var(--shadow-md),
                0 0 0 1px rgba(72, 149, 239, 0.1),
                0 10px 15px -5px rgba(72, 149, 239, 0.2);
        }

        .button.power:hover {
            box-shadow: 
                var(--shadow-lg),
                0 0 0 1px rgba(72, 149, 239, 0.15),
                0 15px 20px -5px rgba(72, 149, 239, 0.25);
        }

        .button.growth {
            background: var(--gradient-growth);
            background-size: 200% 200%;
            background-position: 0% 0%;
            box-shadow: 
                var(--shadow-md),
                0 0 0 1px rgba(58, 12, 163, 0.1),
                0 10px 15px -5px rgba(58, 12, 163, 0.2);
        }

        .button.growth:hover {
            box-shadow: 
                var(--shadow-lg),
                0 0 0 1px rgba(58, 12, 163, 0.15),
                0 15px 20px -5px rgba(58, 12, 163, 0.25);
        }

        .button.enterprise {
            background: var(--gradient-enterprise);
            background-size: 200% 200%;
            background-position: 0% 0%;
            box-shadow: 
                var(--shadow-md),
                0 0 0 1px rgba(86, 11, 173, 0.1),
                0 10px 15px -5px rgba(86, 11, 173, 0.2);
        }

        .button.enterprise:hover {
            box-shadow: 
                var(--shadow-lg),
                0 0 0 1px rgba(86, 11, 173, 0.15),
                0 15px 20px -5px rgba(86, 11, 173, 0.25);
        }

        .button:disabled {
            opacity: 0.7;
            cursor: not-allowed;
            transform: none;
            box-shadow: var(--shadow-sm);
        }

        .error-message {
            color: #e63946;
            padding: 1rem;
            border-radius: var(--border-radius);
            background-color: #fff5f5;
            margin-top: 1rem;
            display: none;
            box-shadow: var(--shadow-sm);
            border: 1px solid rgba(230, 57, 70, 0.2);
        }

        .error-message.visible {
            display: block;
        }

        .enterprise-box {
            background: linear-gradient(135deg, rgba(86, 11, 173, 0.04), rgba(86, 11, 173, 0.01));
            border-radius: var(--border-radius);
            padding: 1.75rem;
            margin-top: 2rem;
            border: 1px solid rgba(86, 11, 173, 0.15);
            text-align: center;
            position: relative;
            overflow: hidden;
            box-shadow: 
                var(--shadow-sm),
                inset 0 1px 0 rgba(255, 255, 255, 0.6);
            transition: transform var(--transition-medium), box-shadow var(--transition-medium);
        }
        
        .enterprise-box:hover {
            transform: translateY(-2px);
            box-shadow: 
                var(--shadow-md),
                0 0 0 1px rgba(86, 11, 173, 0.2),
                inset 0 1px 0 rgba(255, 255, 255, 0.8);
        }

        .enterprise-box::before {
            content: "";
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: var(--gradient-enterprise);
            z-index: 1;
        }

        .enterprise-box h3 {
            font-weight: 700;
            margin-bottom: 0.75rem;
            color: var(--color-enterprise);
            background: var(--gradient-enterprise);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            font-size: 1.25rem;
            letter-spacing: -0.01em;
        }

        .enterprise-box p {
            margin-bottom: 1.5rem;
            font-size: 0.95rem;
            color: #666;
            line-height: 1.6;
        }
        
        /* Loading animation */
        .loading {
            position: relative;
            pointer-events: none;
        }

        .loading::after {
            content: "";
            position: absolute;
            width: 20px;
            height: 20px;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            margin: auto;
            border: 3px solid rgba(255, 255, 255, 0.3);
            border-top-color: white;
            border-radius: 50%;
            animation: button-loading-spinner 1s linear infinite;
        }

        @keyframes button-loading-spinner {
            from {
                transform: rotate(0turn);
            }
            to {
                transform: rotate(1turn);
            }
        }
        
        /* Button pulse animation */
        @keyframes subtle-pulse {
            0% {
                box-shadow: 
                    var(--shadow-md),
                    0 0 0 0 rgba(86, 11, 173, 0.4),
                    0 0 0 1px rgba(86, 11, 173, 0.1),
                    0 10px 15px -5px rgba(86, 11, 173, 0.2);
            }
            70% {
                box-shadow: 
                    var(--shadow-md),
                    0 0 0 10px rgba(86, 11, 173, 0),
                    0 0 0 1px rgba(86, 11, 173, 0.1),
                    0 10px 15px -5px rgba(86, 11, 173, 0.2);
            }
            100% {
                box-shadow: 
                    var(--shadow-md),
                    0 0 0 0 rgba(86, 11, 173, 0),
                    0 0 0 1px rgba(86, 11, 173, 0.1),
                    0 10px 15px -5px rgba(86, 11, 173, 0.2);
            }
        }
        
        .manage-button.pulse-once {
            animation: subtle-pulse 2s ease-out 1;
        }
        
        /* Responsive styles */
        @media (max-width: 768px) {
            .container {
                padding: 1rem;
            }

            .header, .content {
                padding: 1.5rem;
            }
            
            .account-info {
                flex-direction: column;
                align-items: flex-start;
            }
            
            .account-actions {
                margin-left: 0;
                margin-top: 1rem;
                align-self: flex-start;
            }
        }

        /* Styling for the back button inside the card */
        .card-back-button {
            position: absolute; /* Position relative to the card header */
            top: 1.5rem;       /* Adjust vertical position (matches header padding?) */
            left: 1.5rem;        /* Adjust horizontal position */
            color: #555;         /* A slightly muted color */
            text-decoration: none;
            padding: 6px;        /* Smaller padding */
            border-radius: 50%;  /* Circular */
            background-color: transparent; /* No background initially */
            transition: background-color 0.2s, color 0.2s;
            z-index: 10;         /* Ensure it's clickable */
            display: inline-flex; /* Ensures padding wraps icon correctly */
            align-items: center;
            justify-content: center;
        }
        .card-back-button:hover {
             background-color: rgba(0, 0, 0, 0.05); /* Subtle hover background */
             color: #000; /* Darken color on hover */
        }
        .card-back-button svg {
            display: block;
            width: 22px; /* Adjust size as needed */
            height: 22px;
        }

        /* Adjust header padding if needed to make space */
        .card .header {
             padding: 2.5rem 2rem 2rem;
             position: relative; /* Needed for absolute positioning of child */
             text-align: center; /* Keep text centered */
        }
        .card .header h1, .card .header p {
             /* Ensure header text doesn't overlap button if needed */
             /* margin-left: 40px; /* Example if needed */
             /* margin-right: 40px; /* Example if needed */
        }

        /* Style for the Purchase Credits button */
        .purchase-credits-button {
             /* <<< Use a theme color variable >>> */
             background-color: var(--color-secondary); /* Example: Use secondary theme color */
             /* Or use the primary color: background-color: var(--color-primary); */
             /* Or uncomment and use a gradient if preferred: */
             /* background: var(--gradient-secondary); */
             /* background-size: 200% 200%; */
             /* background-position: 0% 0%; */

             /* Ensure text is white */
             color: white;

             /* Keep other base button styles consistent */
             padding: 0.85rem 1.5rem; /* Match .button */
             border-radius: var(--border-radius); /* Match .button */
             font-weight: 600; /* Match .button */
             font-size: 0.95rem; /* Match .button */
             cursor: pointer; /* Match .button */
             transition: /* Match .button */
                transform var(--transition-fast),
                box-shadow var(--transition-fast),
                background-position var(--transition-medium);
             text-align: center; /* Match .button */
             width: 100%; /* Match .button */
             box-shadow: /* Match .button */
                var(--shadow-md),
                0 0 0 1px rgba(58, 12, 163, 0.1), /* Adjust color based on chosen background */
                0 10px 15px -5px rgba(58, 12, 163, 0.2); /* Adjust color */
             letter-spacing: 0.2px; /* Match .button */
             overflow: hidden; /* Match .button */
             border: none; /* Ensure no border */
             display: block; /* Ensure it behaves like other buttons in container */
             text-decoration: none; /* Ensure link looks like button */
        }
        .purchase-credits-button:hover {
             transform: translateY(-3px); /* Match .button:hover */
             box-shadow: /* Match .button:hover */
                var(--shadow-lg),
                0 0 0 1px rgba(58, 12, 163, 0.15), /* Adjust color */
                0 15px 20px -5px rgba(58, 12, 163, 0.25); /* Adjust color */
             background-position: 100% 100%; /* If using gradient */
        }

        .logo-link {
            position: absolute;
            top: 20px;
            left: 20px;
            z-index: 10;
        }

        .top-logo {
            height: 40px;
            width: auto;
            display: block;
        }

        @media (max-width: 768px) {
            .top-logo {
                height: 30px;
            }
        }
    </style>
    <meta http-equiv="Cache-Control" content="no-cache, no-store, must-revalidate">
    <meta http-equiv="Pragma" content="no-cache">
    <meta http-equiv="Expires" content="0">
    <script>
        document.addEventListener("DOMContentLoaded", function() {
            // Check if user ID is properly populated
            console.log("Current User ID:", currentUserId);
            
            // Reset ALL buttons on page load/reload
            function resetAllButtons() {
                // Reset manage payment button
                const manageButton = document.getElementById("manage-billing");
                if (manageButton) {
                    manageButton.disabled = false;
                    manageButton.textContent = "Manage Payment";
                    manageButton.classList.add("pulse-once");
                }
                
                // Reset upgrade buttons
                const powerButton = document.getElementById("checkout-power");
                if (powerButton) {
                    powerButton.disabled = false;
                    powerButton.textContent = "Upgrade to Power - $25/month";
                }
                
                const growthButton = document.getElementById("checkout-growth");
                if (growthButton) {
                    growthButton.disabled = false;
                    growthButton.textContent = "Upgrade to Growth - $50/month";
                }
            }
            
            // Reset buttons immediately when the page loads
            resetAllButtons();
            
            // Also reset when coming back with browser back button
            window.addEventListener('pageshow', function(event) {
                if (event.persisted) {
                    console.log("Page loaded from cache (back button)");
                    resetAllButtons();
                }
            });
            
            // Add manage button click handler
            const manageButton = document.getElementById("manage-billing");
            if (manageButton) {
                manageButton.addEventListener("click", async function() {
                    console.log("Manage button clicked");
                    try {
                        // Show some feedback to the user that something is happening
                        manageButton.disabled = true;
                        manageButton.textContent = "Processing...";
                        
                        const response = await fetch("/payment/create-portal-session", {
                            method: "POST",
                            headers: {
                                "Content-Type": "application/json"
                            },
                            body: JSON.stringify({
                                user_id: currentUserId
                            })
                        });
                        
                        console.log("Response status:", response.status);
                        
                        if (!response.ok) {
                            const errorData = await response.json();
                            console.error("Error data:", errorData);
                            throw new Error(errorData.detail || 'Failed to create portal session');
                        }
                        
                        const data = await response.json();
                        console.log("Response data:", data);
                        
                        if (data.url) {
                            window.location.href = data.url;
                        } else {
                            throw new Error('No redirect URL found in response');
                        }
                    } catch (error) {
                        console.error("Error:", error);
                        const errorMessage = document.getElementById('error-message');
                        errorMessage.textContent = error.message || "An unexpected error occurred";
                        errorMessage.classList.add('visible');
                        
                        // Reset button state
                        manageButton.disabled = false;
                        manageButton.textContent = "Manage Payment";
                    }
                });
            }
            
            // Add parallax effect to buttons
            const buttons = document.querySelectorAll('.button, .manage-button');
            document.addEventListener('mousemove', function(e) {
                const mouseX = e.clientX / window.innerWidth;
                const mouseY = e.clientY / window.innerHeight;
                
                buttons.forEach(button => {
                    const rect = button.getBoundingClientRect();
                    const btnCenterX = rect.left + rect.width / 2;
                    const btnCenterY = rect.top + rect.height / 2;
                    
                    const distanceX = (e.clientX - btnCenterX) / (window.innerWidth / 2) * 5;
                    const distanceY = (e.clientY - btnCenterY) / (window.innerHeight / 2) * 5;
                    
                    if (Math.abs(distanceX) < 20 && Math.abs(distanceY) < 20) {
                        button.style.backgroundPosition = `${50 + distanceX}% ${50 + distanceY}%`;
                    }
                });
            });
        });
    </script>
</head>
<body>
    <a href="{{ frontend_url }}/home" class="logo-link">
        <img src="{{ url_for('static', path='logo_pl.png') }}" alt="Prospect Intelligence Logo" class="top-logo">
    </a>
    
    <div class="container">
        <div class="card">
            <!-- <<< Move Back Button Here >>> -->
            <a href="{{ frontend_url }}/home" title="Return to App Home" class="card-back-button">
                <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor" width="24" height="24">
                    <path d="M20 11H7.83l5.59-5.59L12 4l-8 8 8 8 1.41-1.41L7.83 13H20v-2z"/>
                </svg>
            </a>
            <!-- <<< End Moved Back Button >>> -->

            <header class="header">
                <h1>Manage Billing</h1>
                <p>View your current subscription and manage payment details.</p>
            </header>

            <div class="content">
                <div class="account-info">
                    <div class="account-identity">
                        <div class="account-avatar">{{ first_initial }}</div>
                        <div class="account-details">
                            <h3>{{ user_name }}</h3>
                            <div class="account-id">{{ user_email }}</div>
                        </div>
                    </div>
                    
                    {% if hasCustomer %}
                    <div class="account-actions">
                        <button id="manage-billing" class="manage-button pulse-once">
                            Manage Payment
                        </button>
                    </div>
                    {% endif %}
                </div>

                <div class="current-plan">
                    <div class="plan-label">Current Plan</div>
                    <div class="plan-name">
                        {% if current_plan %}
                            {{ current_plan|title }}
                        {% else %}
                            Free
                        {% endif %}
                    </div>
                    
                    {% if not current_plan or current_plan|lower == 'free' %}
                    <p>Upgrade to access premium features and get more monthly credits</p>
                    {% elif current_plan|lower == 'power' %}
                    <p>You're on the Power plan. Get more features with Growth!</p>
                    {% elif current_plan|lower == 'growth' %}
                    <p>You're on our premium Growth plan with all features unlocked</p>
                    {% endif %}
                </div>

                <!-- <<< START: ADD Single "Purchase Credits" Button (Conditional) >>> -->
                {% if current_plan %} {# Only show if current_plan is not None (i.e., not Free) #}
                <div class="button-container" > <!--  style="margin-top: 1.5rem; margin-bottom: 0.5rem;"> {# Added margin for spacing #} */ -->
                     <a href="/payment/addons?user_id={{ user_id }}" class="button purchase-credits-button"> {# Use base 'button' class + specific class #}
                         Purchase Credits
                     </a>
                </div>
                {% endif %}
                <!-- <<< END: Purchase Credits Button >>> -->

                <div class="button-container">
                    {% if not current_plan or current_plan|lower == 'free' %}
                    <button id="checkout-power" class="button power" data-price-id="price_1R2xcy02BL0Rwhwj77BxxjRz">
                        Upgrade to Power - $25/month
                    </button>
                    <button id="checkout-growth" class="button growth" data-price-id="price_1R2xgW02BL0RwhwjiOQOJHy7">
                        Upgrade to Growth - $50/month
                    </button>
                    {% elif current_plan|lower == 'power' %}
                    <button id="checkout-growth" class="button growth" data-price-id="price_1R2xgW02BL0RwhwjiOQOJHy7">
                        Upgrade to Growth - $50/month
                    </button>
                    {% endif %}
                </div>

                <div class="enterprise-box">
                    <h3>Need More?</h3>
                    <p>Our Enterprise plan offers tailored features, dedicated support, and customized pricing to meet your organization's specific needs.</p>
                    <a href="mailto:<EMAIL>" class="button enterprise">Contact Us</a>
                </div>

                <div id="error-message" class="error-message" role="alert"></div>
                
                <!-- Hidden input for user_id, needed for JavaScript -->
                <input type="hidden" id="user_id_field" value="{{ user_id }}">
            </div>
        </div>
    </div>
</body>
</html>
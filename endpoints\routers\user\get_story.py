import logging
from fastapi import APIRouter, Depends, HTTPException, status
from pydantic import BaseModel, EmailStr, ValidationError, UUID4
from sqlalchemy.exc import SQLAlchemyError, IntegrityError
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.future import select

from endpoints.util.api_responses_util import standard_responses
from endpoints.util.database import DatabaseSessionManager
from endpoints.models.schemas import (
    CustomDataResponse,
    PublicStoryResponse,
)
from endpoints.models.stories import Story, RunStatus
from endpoints.util.story_response_builder import create_public_story_response, create_prospect_detail
from endpoints.util.subscription_helper import get_active_subscription

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# -----------------------
# Router Initialization
# -----------------------
get_story_router = APIRouter(prefix="/user", tags=["User"])


@get_story_router.get(
    "/get_story/{run_id}",
    response_model=CustomDataResponse[PublicStoryResponse],
    responses=standard_responses(
        error_404_description="Story not found",
        error_500_description="Database error occurred / Unexpected Error Occurred",
        error_422_description="Invalid input provided",
    ),
)
async def get_story(
    run_id: UUID4,
    db: AsyncSession = Depends(DatabaseSessionManager.get_session),
):
    try:
        logger.info(f"Fetching story with ID: {run_id}")

        # Query the story and user data
        result = await db.execute(select(Story).filter(Story.run_id == run_id))
        story = result.scalar_one_or_none()
        if not story:
            logger.error(f"❌❌❌ Story with ID {run_id} not found")
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND, detail="Story not found"
            )
        if story.status == RunStatus.in_progress:
            logger.info(f"Story {run_id} is still in progress")
            return CustomDataResponse(
                status_code=status.HTTP_202_ACCEPTED,
                detail="Story generation in progress",
                data=PublicStoryResponse(run_id=story.run_id,
                                         user_id=story.user_id,
                                         status=story.status.value,
                                         title=(story.title or "").title(),
                                         overall_summary=None,
                                         executive_summary=None,
                                         prospect=create_prospect_detail(story),
                                         tabs= None,
                                         created_at=story.created_at,
                                         updated_at=story.updated_at,
                                         ),
            )
        elif story.status == RunStatus.failed:
            logger.error(f"❌❌❌ Story generation failed for {run_id}")
            return CustomDataResponse(
                status_code=status.HTTP_200_OK,
                detail="Story generation failed",
                data=PublicStoryResponse(
                    run_id=story.run_id,
                    user_id=story.user_id,
                    status=story.status.value,  # "failed"
                    title=(story.title or "Failed Story").title(),
                    overall_summary=None,
                    executive_summary=None,
                    prospect=create_prospect_detail(story),
                    linkedin_url=story.prospect_linkedin,
                    profile_picture_url=story.profile_picture_url,
                    tabs=None,
                    created_at=story.created_at,
                    updated_at=story.updated_at,
                ),
            )
        # Get user's active subscription
        try:
            subscription = await get_active_subscription(db, story.user_id)
        except SQLAlchemyError as e:
            logger.error(f"❌❌❌ Database error while fetching subscription: {e}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Database operation failed",
            )
        except ValueError as e:
            logger.error(f"❌❌❌ Validation error: {e}")
            raise HTTPException(
                status_code=status.HTTP_422_UNPROCESSABLE_ENTITY, detail=str(e)
            )
        except Exception as e:
            logger.error(f"❌❌❌ Unexpected error: {e}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="An unexpected error occurred",
            )

        if not subscription:
            logger.warning(f"No active subscription found for user {story.user_id}")
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="No active subscription found",
            )

        # Create story response using the utility function
        story_response = create_public_story_response(story, subscription)

        logger.info("✅✅✅ Story found successfully")
        return CustomDataResponse(
            status_code=status.HTTP_200_OK,
            detail="Story retrieved successfully",
            data=story_response,
        )

    except SQLAlchemyError as e:
        logger.exception(f"❌❌❌ Database error: {e}")
        await db.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Database operation failed",
        )

from sqlalchemy import Column, String, Boolean, TIMESTAMP, text, ForeignKey
from sqlalchemy.dialects.postgresql import UUID
from endpoints.models.base import Base

import uuid


class User(Base):
    __tablename__ = "users"

    user_id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    auth_provider = Column(String(50), nullable=False)
    firebase_uid = Column(String(28), nullable=False, unique=True)
    auth_email = Column(String(255), nullable=False, unique=True)
    first_name = Column(String(255))
    last_name = Column(String(255))
    company_name = Column(String(255))
    business_email = Column(String(255), unique=True)
    crm_user_sync_pending = Column(Boolean, default=True)
    active_status = Column(Boolean, default=False)
    plan_id = Column(
        UUID(as_uuid=True),
        ForeignKey("sales_smartstory_plans.plan_id", ondelete="CASCADE"),
        nullable=True,
        default=None,
    )
    is_paid_user = Column(Boolean, default=False)
    is_user_provisioned = Column(Boolean, default=False)
    is_user_locked_in = Column(Boolean, default=False)
    stripe_customer_id = Column(String(255))
    created_at = Column(TIMESTAMP(timezone=True), server_default=text("NOW()"))
    updated_at = Column(
        TIMESTAMP(timezone=True), server_default=text("NOW()"), onupdate=text("NOW()")
    )

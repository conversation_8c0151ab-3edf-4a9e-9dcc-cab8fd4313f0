to test locally, modify url, host and port as needed 
```bash
DATABASE_URL=postgresql+asyncpg://postgres:password@postgres:5432/postgres \
HOST=0.0.0.0 \
PORT=8000 \
docker-compose up
```
# [Database Design and Model 🔗](local-testing/README)
![Database Diagram](local-testing/Database_Design.png "DB Model")

# FastAPI Template

This sample repo contains the recommended structure for a Python FastAPI project. In this sample, we use `fastapi` to build a web application and the `pytest` to run tests.

For a more in-depth tutorial, see our [Fast API tutorial](https://code.visualstudio.com/docs/python/tutorial-fastapi).

The code in this repo aims to follow Python style guidelines as outlined in [PEP 8](https://peps.python.org/pep-0008/).

## Project Structure

```plaintext
prospectintel-endpoint-services
├── .github
│   └── workflows
│       └── deploy.yml
├── terraform
│   ├── env
│   │   ├── test
│   │   └── prod
│   ├── main.tf
│   ├── variables.tf
│   ├── outputs.tf
│   └── provider.tf
├── endpoints
│   ├── main.py
│   ├── routers
│   │   ├── __init__.py
│   │   └── save_user.py
│   ├── models
│   │   ├── __init__.py
│   │   └── users.py
│   ├── database.py
│   ├── schemas.py
│   └── tests
│       └── test_main.py
├── Dockerfile
├── requirements.txt
└── README.md
```


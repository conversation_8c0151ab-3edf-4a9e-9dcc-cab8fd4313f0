services:
  postgres:
    image: postgres:16
    container_name: postgres-db
    environment:
      POSTGRES_USER: ${POSTGRES_USER:-postgres}
      POSTGRES_PASSWORD: ${POSTGRES_PASSWORD:-password}
      POSTGRES_DB: ${POSTGRES_DB:-postgres}
    ports:
      - "${POSTGRES_PORT:-5432}:5432"
    volumes:
      - ./docker/init-scripts/:/docker-entrypoint-initdb.d
      - type: tmpfs
        target: /var/lib/postgresql/data
        tmpfs:
          size: 100m
    networks:
      - fastapi-network

  pgadmin:
    image: dpage/pgadmin4:latest
    container_name: pi_pgadmin
    environment:
      PGADMIN_DEFAULT_EMAIL: ${PGADMIN_DEFAULT_EMAIL:-<EMAIL>}
      PGADMIN_DEFAULT_PASSWORD: ${PGADMIN_DEFAULT_PASSWORD:-admin}
    ports:
      - "${PGADMIN_PORT:-5050}:80"
    networks:
      - fastapi-network
    depends_on:
      - postgres
    volumes:
      - ./docker/pgadmindata:/var/lib/pgadmin

  fastapi:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: fastapi-app
    env_file:
      - endpoints/.env
    ports:
      - "${PORT:-8000}:${PORT:-8000}"
    volumes:
      - ./service_account.json:/app/keys/service_account.json:ro
      - ./endpoints/static:/app/endpoints/static:ro
    depends_on:
      - postgres
    networks:
      - fastapi-network

networks:
  fastapi-network:
    driver: bridge

volumes:
  postgres-data:
    name: postgres-data

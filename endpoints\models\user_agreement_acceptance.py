from sqlalchemy import <PERSON>umn, <PERSON>olean, String, TIMESTAMP, text, ForeignKey, Integer
from sqlalchemy.dialects.postgresql import UUID
import uuid

from endpoints.models.base import Base


class UserAgreementAcceptance(Base):
    __tablename__ = "user_agreement_acceptance"

    id = Column(Integer, primary_key=True, autoincrement=True)  # Add this primary key
    user_id = Column(UUID(as_uuid=True), ForeignKey("users.user_id"), nullable=False)
    user_agreement_accepted = Column(Boolean, default=False)
    ip_address = Column(String(255))
    device_type = Column(String(255))
    created_at = Column(TIMESTAMP(timezone=True), server_default=text("NOW()"))
    updated_at = Column(TIMESTAMP(timezone=True), server_default=text("NOW()"))

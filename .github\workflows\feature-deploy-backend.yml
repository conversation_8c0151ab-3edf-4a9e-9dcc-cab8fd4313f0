name: Deploy Backend

on:
  push:
    branches: [ 'feat-*', 'ft-*' ] # Matched frontend pattern
  repository_dispatch:
    types: [ deploy-backend ]

concurrency:
  group: backend-${{ github.ref }} # Concurrency group specific to backend
  cancel-in-progress: true

env:
  REGION: us-central1
  SERVICE_PORT: 8000 # Backend service port
  GAR_REPOSITORY: cloud-run-source-deploy # Consistent GAR repo name

jobs:
  deploy:
    runs-on: ubuntu-latest
    permissions:
      contents: read
      id-token: write # Required for google-github-actions/auth (OIDC)
    env:
      CONFIG_ENVIRONMENT:  dev
      jwt_secret_gh:       'JWT_SECRET'


    strategy:
      fail-fast: false
      matrix:
        include:

# # #          # ─── Mario<PERSON> ────────────────────────────────────────────────
#            - project_id:          'mariokart-********'
#              branch_suffix:       'mariokart'
#              config_project:      'general_dev'
#              service_account:     '<EMAIL>'
#              builder_service_account:     '<EMAIL>'
#              wrapper_function_url_prefix: https://story-generation-wrapper-ft-355-395-396-dev
#              db_instance_name: 'prospect-intel-database'
#              story_generation_sa_email: <EMAIL>
#              enable_stripe: true

#
       # # ─── Prospectintel ──────────────────────────────────────────────────
          - project_id:          'sales-prospectintel-********'
            branch_suffix:       'sales-prospectintel'
            config_project: 'sales-prospectintel'
            service_account:      <EMAIL>
            builder_service_account:     '<EMAIL>'
            wrapper_function_url_prefix: https://story-generation-wrapper-ft-408-436-fix-dev
            db_instance_name: 'sales-prospectintel-instance'
            story_generation_sa_email: <EMAIL>
            enable_stripe: false # Example: Stripe disabled


    steps:
      # 0⃣ Cache popular tool directories ────────────────────────────
      - uses: actions/cache@v4
        with:
          path: |
            ~/.docker
            ~/.cache
          key: ${{ runner.os }}-backend-tooling-${{ hashFiles('**/Dockerfile') }}

      # 1⃣ Checkout
      - name: Checkout
        uses: actions/checkout@v4

      # 2⃣ Sanitize branch
      - name: Sanitise branch
        run: echo "SANITIZED_BRANCH=$(echo "${GITHUB_REF_NAME}" | tr '/_' '-' | tr '/.' '-')" >> $GITHUB_ENV


      # ❷ GCP auth
      - name: Authenticate to Google Cloud via WIF
        id: auth
        uses: google-github-actions/auth@v2
        with:
          workload_identity_provider: projects/*************/locations/global/workloadIdentityPools/github-actions-pool/providers/github-prospectintel
          service_account: ${{ matrix.builder_service_account }}
          project_id: ${{ matrix.project_id }}
          create_credentials_file: true
          export_environment_variables: true


      # Setup gcloud CLI
      - name: Set up Cloud SDK
        uses: google-github-actions/setup-gcloud@v2
        with:
          project_id: ${{ matrix.project_id }}

      # ❷ Get Project Number (needed for constructing some URLs/IDs)
      - name: Get Project Number
        id: project_num
        run: |
          echo "Fetching project number for ${{ matrix.project_id }}"
          NUM=$(gcloud projects describe ${{ matrix.project_id }} --format='value(projectNumber)' --quiet)
          if [[ -z "$NUM" ]]; then
            echo "❌ Failed to get Project Number for ${{ matrix.project_id }}"
            exit 1
          fi
          echo "Project Number: $NUM"
          # Make available to subsequent steps AND env context
          echo "NUMERICAL_PROJECT_ID=$NUM" >> $GITHUB_ENV
          echo "project_number=$NUM" >> $GITHUB_OUTPUT

      # ❸ Ensure GAR Repository Exists
      - name: Ensure GAR Repository Exists
        run: |
          echo "Checking if GAR repository '${{ env.GAR_REPOSITORY }}' exists in project '${{ matrix.project_id }}' region '${{ env.REGION }}'..."
          if gcloud artifacts repositories describe "${{ env.GAR_REPOSITORY }}" \
            --project="${{ matrix.project_id }}" \
            --location="${{ env.REGION }}" \
            --quiet > /dev/null 2>&1; then
            echo "✅ GAR repository '${{ env.GAR_REPOSITORY }}' already exists."
          else  
            echo "Repository '${{ env.GAR_REPOSITORY }}' not found. Creating it..."
            gcloud artifacts repositories create "${{ env.GAR_REPOSITORY }}" \
              --project="${{ matrix.project_id }}" \
              --repository-format="docker" \
              --location="${{ env.REGION }}" \
              --description="Docker repository for Prospect Intel Backend images" \
              --quiet
            echo "✅ GAR repository '${{ env.GAR_REPOSITORY }}' created."
          fi

      # Configure Docker to authenticate with Artifact Registry
      - name: Configure Docker for Artifact Registry
        run: gcloud auth configure-docker ${{ env.REGION }}-docker.pkg.dev --quiet

      # NEW STEP: Ensure Stripe Webhook GCP Secret Exists (Placeholder)
      - name: Ensure Stripe Webhook GCP Secret Exists
        if: matrix.enable_stripe == true
        env:
          GCP_SECRET_NAME_STRIPE_WH: "stripe-webhook-signing-secret-${{ env.CONFIG_ENVIRONMENT }}"
          GCP_PROJECT_ID: ${{ matrix.project_id }}
        run: |
          echo "Ensuring GCP Secret Manager secret '${GCP_SECRET_NAME_STRIPE_WH}' exists..."
          if ! gcloud secrets describe "${GCP_SECRET_NAME_STRIPE_WH}" --project="${GCP_PROJECT_ID}" >/dev/null 2>&1; then
            echo "Secret does not exist. Creating with a placeholder..."
            echo -n "INITIAL_PLACEHOLDER_AWAITING_STRIPE" | gcloud secrets create "${GCP_SECRET_NAME_STRIPE_WH}" \
              --project="${GCP_PROJECT_ID}" \
              --replication-policy="automatic" \
              --data-file=- \
              --quiet
            if [ $? -ne 0 ]; then
              echo "❌ Failed to create placeholder GCP Secret ${GCP_SECRET_NAME_STRIPE_WH}." >&2
              exit 1
            fi
            echo "✅ Placeholder secret created."
          else
            echo "✅ Secret already exists."
          fi

      # ❹ Fetch Secrets from GCP Secret Manager
      - name: Fetch GCP Secrets
        id: fetch_secrets
        run: |
          echo "Preparing environment variables for project: ${{ matrix.project_id }}"
          # Construct the Cloud Run function URL
          FUNC_URL=${{ matrix.wrapper_function_url_prefix }}-${{ env.NUMERICAL_PROJECT_ID }}.${{ env.REGION }}.run.app 
          echo "Setting FUNC_URL (Cloud Run function URL) in environment..."
          echo "FUNC_URL=${FUNC_URL}" >> $GITHUB_ENV

          echo "✅ Environment variables prepared for deployment."

      # ❺ Build and Push Docker Image to Artifact Registry
      - name: Define Image Tag
        id: image_tag
        run: |
          TAG="${{ env.REGION }}-docker.pkg.dev/${{ matrix.project_id }}/${{ env.GAR_REPOSITORY }}/prospect-intel-backend-${{ env.SANITIZED_BRANCH }}-${{ env.CONFIG_ENVIRONMENT }}:${{ github.sha }}"
          echo "image_tag=$TAG" >> $GITHUB_OUTPUT
          echo "Image Tag: $TAG"

      - name: Build Docker Image
        run: docker build -t "${{ steps.image_tag.outputs.image_tag }}" .

      - name: Push Docker Image
        run: docker push "${{ steps.image_tag.outputs.image_tag }}"

      # ❻ Deploy to Cloud Run using the pre-built image
      - name: Deploy to Cloud Run
        id: deploy
        uses: google-github-actions/deploy-cloudrun@v2
        with:
          project_id: ${{ matrix.project_id }}
          service:    "prospect-intel-backend-${{ env.SANITIZED_BRANCH }}-${{ env.CONFIG_ENVIRONMENT }}"
          image:      "${{ steps.image_tag.outputs.image_tag }}"
          region:     ${{ env.REGION }}
          env_vars: |
            STRIPE_API_KEY=${{ secrets.STRIPE_API_KEY }}
            WRAPPER_FUNCTION_URL=${{ env.FUNC_URL }}
            CONFIG_PROJECT=${{ matrix.config_project }}
            CONFIG_ENVIRONMENT=${{ env.CONFIG_ENVIRONMENT }}
            JWT_SECRET=${{ secrets[env.jwt_secret_gh] }}
            STORY_GENERATION_SA_EMAIL=${{ matrix.story_generation_sa_email }}
          secrets: | 
            DATABASE_URL= DATABASE_URL_DEV:latest
            ${{ matrix.enable_stripe && format('STRIPE_WEBHOOK_SECRET=stripe-webhook-signing-secret-{0}:latest', env.CONFIG_ENVIRONMENT) || '' }}
          flags: >-
            --platform=managed
            --port=${{ env.SERVICE_PORT }}
            --service-account=${{ matrix.service_account }}
            --add-cloudsql-instances=${{ matrix.project_id }}:${{ env.REGION }}:${{ matrix.db_instance_name }}
            --allow-unauthenticated

      # ❼ Export Backend URL and save to Secret Manager
      - name: Save Backend URL to Secret Manager
        run: |
          BACKEND_URL="${{ steps.deploy.outputs.url }}"
          # Use a consistent secret name pattern, incorporating the branch suffix
          SECRET_NAME="backend-url-${{ matrix.branch_suffix }}-${{ env.CONFIG_ENVIRONMENT }}"
          echo "Backend deployed to: $BACKEND_URL"
          echo "Saving URL to secret: $SECRET_NAME in project ${{ matrix.project_id }}"
  
          # Use temporary file for secret data
          echo "$BACKEND_URL" > backend_url_${{ matrix.branch_suffix }}_${{ env.CONFIG_ENVIRONMENT }}.txt

          # Create secret if it doesn't exist (idempotent)
          gcloud secrets create "$SECRET_NAME" \
            --project="${{ matrix.project_id }}" \
            --replication-policy="automatic" \
            --quiet || echo "Secret $SECRET_NAME already exists."

          # Add a new version with the latest URL
          gcloud secrets versions add "$SECRET_NAME" \
            --project="${{ matrix.project_id }}" \
            --data-file="backend_url_${{ matrix.branch_suffix }}_${{ env.CONFIG_ENVIRONMENT }}.txt" \
            --quiet

          echo "✅ Backend URL saved to secret: $SECRET_NAME"

      # ❽ Configure Stripe Webhook using curl
      - name: Configure Stripe Webhook
        id: configure_stripe_webhook
        if: success() && matrix.enable_stripe == true
        env:
          STRIPE_API_KEY: ${{ secrets.STRIPE_API_KEY }}
          BACKEND_URL: ${{ steps.deploy.outputs.url }}
          WEBHOOK_PATH: "/payment/webhook"
          WEBHOOK_EVENTS_LIST: "checkout.session.completed,invoice.payment_succeeded,invoice.payment_failed,customer.subscription.created,customer.subscription.updated,customer.subscription.deleted,payment_intent.succeeded,payment_intent.payment_failed"
          PROJECT_ID_FOR_STRIPE: ${{ matrix.project_id }}
          BRANCH_SUFFIX_FOR_STRIPE: ${{ matrix.branch_suffix }}
          ENVIRONMENT_FOR_STRIPE: ${{ env.CONFIG_ENVIRONMENT }}
        run: |
          TARGET_WEBHOOK_URL="${BACKEND_URL%/}${WEBHOOK_PATH}"
          STRIPE_WEBHOOK_DESCRIPTION="Backend: ${PROJECT_ID_FOR_STRIPE} (${BRANCH_SUFFIX_FOR_STRIPE}) - Env: ${ENVIRONMENT_FOR_STRIPE} - GHA Deployed"
          NEW_SIGNING_SECRET_TO_SAVE="" # Initialize to empty

          echo "Target Stripe Webhook URL: ${TARGET_WEBHOOK_URL}"
          echo "Stripe Webhook Description for new creation: ${STRIPE_WEBHOOK_DESCRIPTION}"
          echo "Desired events for new creation: ${WEBHOOK_EVENTS_LIST}"

          echo "Fetching existing webhook endpoints from Stripe to check for URL: ${TARGET_WEBHOOK_URL}..."
          EXISTING_WEBHOOKS_RESPONSE=$(curl -s -G "https://api.stripe.com/v1/webhook_endpoints" \
            -u "${STRIPE_API_KEY}:" \
            --data-urlencode "limit"="100") # Adjust limit if you have many webhooks

          if ! echo "${EXISTING_WEBHOOKS_RESPONSE}" | jq -e '.data' > /dev/null; then
            echo "Error: Failed to fetch webhooks or unexpected response from Stripe." >&2
            echo "Response: ${EXISTING_WEBHOOKS_RESPONSE}"
            exit 1 # FAIL THE STEP
          fi

          # Use jq to find if the webhook URL already exists
          EXISTING_WEBHOOK_ID=$(echo "${EXISTING_WEBHOOKS_RESPONSE}" | \
            jq -r --arg url "${TARGET_WEBHOOK_URL}" '.data[] | select(.url == $url) | .id' | head -n 1)
          
          if [ -n "${EXISTING_WEBHOOK_ID}" ]; then
            echo "✅ Stripe webhook for URL ${TARGET_WEBHOOK_URL} already exists with ID: ${EXISTING_WEBHOOK_ID}."
            echo "   No action will be taken (no update, no new secret)."
            # NEW_SIGNING_SECRET_TO_SAVE will remain empty
          else # Webhook does not exist, create it
            echo "Stripe webhook for ${TARGET_WEBHOOK_URL} not found. Creating it..."
            
            EVENT_DATA_PARAMS_CREATE=""
            IFS=',' read -ra ADDR_CREATE <<< "$WEBHOOK_EVENTS_LIST"
            for i_create in "${ADDR_CREATE[@]}"; do
              EVENT_DATA_PARAMS_CREATE+="--data-urlencode enabled_events[]=${i_create} "
            done

            CREATE_RESPONSE=$(curl -s -X POST "https://api.stripe.com/v1/webhook_endpoints" \
              -u "${STRIPE_API_KEY}:" \
              --data-urlencode "url"="${TARGET_WEBHOOK_URL}" \
              ${EVENT_DATA_PARAMS_CREATE} \
              --data-urlencode "description"="${STRIPE_WEBHOOK_DESCRIPTION}")

            if ! echo "${CREATE_RESPONSE}" | jq -e '.id' > /dev/null; then
              echo "Error: Failed to create webhook or unexpected response from Stripe." >&2
              echo "Response: ${CREATE_RESPONSE}"
              exit 1 # FAIL THE STEP
            fi
            
            NEW_WEBHOOK_ID=$(echo "${CREATE_RESPONSE}" | jq -r '.id')
            NEW_SIGNING_SECRET_TO_SAVE=$(echo "${CREATE_RESPONSE}" | jq -r '.secret') # Capture the new secret
            echo "✅ Successfully CREATED Stripe webhook ID: ${NEW_WEBHOOK_ID}"
            echo "   Description: ${STRIPE_WEBHOOK_DESCRIPTION}"
            echo "   Generated NEW Signing Secret (will be processed by next step)."
          fi
          
          # Output the new secret (if any) for the next step. It will be empty if webhook already existed.
          echo "::set-output name=new_stripe_signing_secret::${NEW_SIGNING_SECRET_TO_SAVE}"

      # ❾ Save Stripe Signing Secret to GCP Secret Manager
      - name: Update Stripe Signing Secret in GCP Secret Manager
        if: success() && steps.configure_stripe_webhook.outputs.new_stripe_signing_secret != '' && matrix.enable_stripe == true
        env:
          NEW_STRIPE_SECRET_VALUE: ${{ steps.configure_stripe_webhook.outputs.new_stripe_signing_secret }}
          GCP_SECRET_NAME_STRIPE_WH: "stripe-webhook-signing-secret-${{ env.CONFIG_ENVIRONMENT }}"
          GCP_PROJECT_ID: ${{ matrix.project_id }}
        run: |
          echo "Stripe signing secret was generated/updated. Adding new version to GCP Secret: ${GCP_SECRET_NAME_STRIPE_WH}"
          # No need to check if value is same here, as this step only runs if Stripe generated a NEW secret.
          # The placeholder will be overwritten.
          echo -n "${NEW_STRIPE_SECRET_VALUE}" | gcloud secrets versions add "${GCP_SECRET_NAME_STRIPE_WH}" \
            --project="${GCP_PROJECT_ID}" \
            --data-file=- \
            --quiet
          if [ $? -ne 0 ]; then
            echo "❌ Failed to add new version to GCP Secret ${GCP_SECRET_NAME_STRIPE_WH}." >&2
            exit 1
          fi
          echo "✅ Stripe signing secret updated in GCP Secret Manager: ${GCP_SECRET_NAME_STRIPE_WH}"

      # ❾ Upload Backend URL as artifact
      - name: Upload Backend URL Artifact
        uses: actions/upload-artifact@v4
        with:
          name: "backend-url-${{ matrix.branch_suffix }}-${{ env.CONFIG_ENVIRONMENT }}"
          path: backend_url_${{ matrix.branch_suffix }}_${{ env.CONFIG_ENVIRONMENT }}.txt
          if-no-files-found: error

#      # ❾ Trigger Frontend Workflow (Optional - Requires PAT)
#      - name: Trigger Frontend Deploy
#        if: github.event_name == 'push' # Example: only trigger on push
#        uses: peter-evans/repository-dispatch@v3
#        with:
#          token: ${{ secrets.REPO_ACCESS_TOKEN }} # Needs a PAT with repo scope
#          repository: zengearsinc/prospectintel-frontend # Replace with your frontend repo path
#          event-type: deploy-frontend
#          client-payload: '{ "ref": "refs/heads/feat-frontend-${{ matrix.branch_suffix }}", "triggering_branch": "${{ github.ref_name }}", "project_id": "${{ matrix.project_id }}" }'

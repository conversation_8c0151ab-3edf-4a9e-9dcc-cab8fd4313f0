from pydantic import UUID4
from sqlalchemy.exc import SQLAlchemyError
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select
from endpoints.models.sales_smartstory_subscriptions import SalesSmartStorySubscription
import logging

from endpoints.models.users import User

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


async def get_user_credits(db: AsyncSession, user_id: UUID4) -> int:
    """
    Get the remaining credits for a user from their active subscription.

    Args:
        db: AsyncSession - The database session
        user_id: UUID4 - The ID of the user

    Returns:
        int: The number of available credits, or 0 if no active subscription is found
    """
    try:
        # Query the active subscription for the user
        result = await db.execute(
            select(SalesSmartStorySubscription.available_credit).filter(
                SalesSmartStorySubscription.user_id == user_id,
                SalesSmartStorySubscription.active_subscription == True,
            )
        )

        subscription = result.scalar_one_or_none()

        if subscription is None:
            logger.info(f"✅✅✅ No active subscription found for user {user_id}")
            return 0

        # Convert Numeric to int
        available_credits = int(subscription) if subscription else 0
        logger.info(f"✅✅✅Retrieved {available_credits} credits for user {user_id}")
        return available_credits

    except SQLAlchemyError as e:
        logger.error(f"❌❌❌ Database error: {e}")
        return 0
    except Exception as e:
        logger.error(f"❌❌❌ Error calculating user credits: {e}")
        return 0


async def deduct_user_credits(
    db: AsyncSession, user_id: UUID4, credits: int = 1
) -> int:
    """
    Deduct credits from user's account and return remaining credits.

    Args:
        db: AsyncSession - The database session
        user_id: UUID4 - The ID of the user
        credits: int - Number of credits to deduct (default: 1)

    Returns:
        int: The number of remaining credits after deduction, or 0 if operation fails
    """
    try:
        logger.info(f"Attempting to deduct {credits} credits for user {user_id}")

        # Query the active subscription
        result = await db.execute(
            select(SalesSmartStorySubscription).filter(
                SalesSmartStorySubscription.user_id == user_id,
                SalesSmartStorySubscription.active_subscription == True,
            )
        )

        subscription = result.scalar_one_or_none()

        if subscription is None:
            logger.error(f"❌❌❌ No active subscription found for user {user_id}")
            return 0

        available_credits = (
            int(subscription.available_credit) if subscription.available_credit else 0
        )

        if available_credits >= credits:
            # Update the available credits in the subscription
            subscription.available_credit = available_credits - credits
            await db.commit()

            logger.info(
                f"✅✅✅ Deducted {credits} credits for user {user_id}. Remaining credits: {subscription.available_credit}"
            )
            return int(subscription.available_credit)
        else:
            logger.error(
                f"❌❌❌ Insufficient credits. Available: {available_credits}, Required: {credits}"
            )
            return 0

    except SQLAlchemyError as e:
        await db.rollback()
        logger.error(f"❌❌❌ Database error: {e}")
        return 0
    except Exception as e:
        await db.rollback()
        logger.error(f"❌❌❌ Error deducting user credits: {e}")
        return 0
